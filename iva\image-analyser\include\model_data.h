/**
 * Project IVA (image analyser)
 */
#pragma once

#include <vector>
#include <functional>
#include "opencv2/core.hpp"

namespace ia
{
	/**
	* 模型输入
	*/
	struct ModelInputData
	{
		cv::Mat image;
		void* userData;
        int channel = 0;
	};

	/**
	* 模型输出
	*/
	typedef std::vector<std::vector<float>> ModelOutput;
	struct ModelOutputData
	{
		ModelOutput result;
		void* userData;
	};

	/**
	* 模型输出回调
	*/
	typedef std::function<void(ModelOutputData)> ModelOutputCallback;

	/**
	* 特征向量
	*/
	using FeatureVector = std::vector<float>;

	/**
	* 目标特征
	*/
	struct FeatureOutputData
	{
		FeatureVector feature;		// 特征
		std::string plateColor;	// 车牌颜色
		std::string plateNum;	// 车牌数字
		void* userData;
	};

	/**
	* 特征数据输出回调
	*/
	typedef std::function<void(FeatureOutputData)> FeatureOutputCallback;
}
