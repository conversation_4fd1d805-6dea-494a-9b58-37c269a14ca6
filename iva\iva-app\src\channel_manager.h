#pragma once
#include "protocol/all.h"

namespace iva::protocol
{
    /**
     * @brief 注册接收信号回调
     * @param processID   进程号ID
     * @param deviceID    GPU设备ID
     * @param channelSize 支持的通道数量
     */
    void registerProtocols(int processID, int deviceID, int channelSize);

    /**
     * @brief 恢复通道检测
     */
    void restoreChannel(const network::ChannelRestoreConf& channelConfig);

    /**
     * @brief reset channel
     */
    void resetChannel(int channelId);

    /**
     * @brief 暂停通道检测
     * @param[in] channelId: 通道id
     * @param[in] videoId:   视频id
     * @param[in] reason:    暂停原因 0其他，1手工，2画检测区,3偏移
     */
    void pauseChannel(int channelId, int videoId, int reason);

    /**
     * @brief 更新系统配置
     */
    void updateSystemConfig(const network::SystemConfig& config);

    /**
     * @brief 更新灵敏度参数
     */
    void updateAlgorithmParam(const network::AlgorithmParam& param);

    /**
     * @brief 更新通道基本配置参数
     */
    void updateBaseChannelConfig(const network::VecChannelBasicConf& configs);

    /**
     * @brief 更新通道检测参数
     */
    void updateDetectParam(const network::ChannelDetectParam& param);

    /**
     * @brief 请求结构化视频
     */
    void requestIvaVideo(int index, int streamId);

    void requestSnap(int channelId);

}
