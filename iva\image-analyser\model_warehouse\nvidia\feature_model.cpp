#include "feature_model.h"

#include <utility>
#include "mutex"

using namespace std;
namespace model_warehouse
{
    FeatureModel::FeatureModel(std::string modelPath, int deviceID) : BaseModel(std::move(modelPath), deviceID, NetworkInputType::CHW){}

    /**
     * 模型加载，资源初始化
     */
    void FeatureModel::initResource()
    {
        if (!modelInited)
        {
            predictor.init(std::move(modelPath), deviceId);

            initInputDimensions();
            initOutputDimensions();

            scoreDataHost = std::shared_ptr<float[]>(new float[batchSize * outputChannel]);
            CHECK_CUDA_STATUS(cudaMalloc(&scoreDataDevice, batchSize * outputChannel * sizeof(float)), "cudaMalloc failed", true);
            CHECK_CUDA_STATUS(cudaMalloc(&imageDataDevice, batchSize * inputWidth * inputHeight * inputChannel * sizeof(float)), "cudaMalloc failed", true);

            modelInited = true;
        }
    }

    /**
     * @brief                  执行一次图像推理流程：预处理、推理、后处理输出
     * @param[in] images       原始输入图像
     * @param[in]  padding     是否进行图像padding
     * @return                 推理后处理输出结果
     */
    FEATURE_RESULT FeatureModel::infer(std::vector<cv::Mat> images, bool padding)
    {
        vector<float> imageData;
        preprocess(images, padding, false, imageData);

        CHECK_CUDA_STATUS(cudaMemcpy(imageDataDevice, imageData.data(), imageData.size() * sizeof(float), cudaMemcpyHostToDevice), "Failed to copy image data to device", false);
        vector<void *> buffers = {imageDataDevice, scoreDataDevice };
        if (!predictor.infer(buffers, batchSize))
            return {};

        return postprocess((int)images.size());
    }

    /**
     * @brief                  推理后处理，处理推理输出结果
     * @param[in] imageSize    推理图像数量
     * @return                 推理后处理输出结果
     */
    FEATURE_RESULT FeatureModel::postprocess(int imageSize)
    {
        std::vector<std::vector<float>> scores;
        CHECK_CUDA_STATUS(cudaMemcpy(scoreDataHost.get(), scoreDataDevice, batchSize * outputChannel * sizeof(float), cudaMemcpyDeviceToHost), "Failed to copy score data to host", false);
        for(int b=0; b < batchSize; b++)
        {
            std::vector<float> score;
            for(int i=0; i < outputChannel; i++)
            {
                score.push_back(scoreDataHost[i]);
            }
            scores.push_back(score);
        }
        return scores;
    }
}
