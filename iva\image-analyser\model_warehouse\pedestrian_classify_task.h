#pragma once
#include "class_task_base.h"

namespace ia
{
    /**
     * 行人分类器行为类
     */
    class PedestrianClassifyAction : public ClassifierActionBase {
    public:
        explicit PedestrianClassifyAction(int deviceID = 0) 
            : ClassifierActionBase(deviceID, 3, "personclothes") {}
    };

    /**
     * 行人分类器管理类
     */
    using PedestrianClassifyMgr = ClassifierManagerBase<PedestrianClassifyAction>;
} 