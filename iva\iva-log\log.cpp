/****************************************************************************
*
*	 Image analysis logging module
*
******************************************************************************/
#include <stdio.h>
#ifdef _MSC_VER 
#include <direct.h>
#else
#include <unistd.h>
#endif
#include "log.h"
#include "spdlog/sinks/daily_file_sink.h"
#include "spdlog/sinks/stdout_color_sinks.h"
#include <map>

#ifndef _MSC_VER
#define _access access
#endif

static std::map<IvaLogSwitch, bool> logSwitchs;

void setupLog(const char * name, std::string path)
{
	std::shared_ptr<spdlog::logger> log = spdlog::get(name);
	if (log == NULL)
	{
		if (_access("log", 0) == -1)
		{
#ifdef _MSC_VER
			_mkdir("log");
#else
			mkdir("log", 0777);
#endif
		}

		auto stdout_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt >();
		stdout_sink->set_level(spdlog::level::trace);

		char log_path[256];
        if (path.empty())
		    sprintf(log_path, "log/%s_log.txt", name);
        else
            sprintf(log_path, "log/%s_log.txt", path.c_str());

		auto daily_logger = std::make_shared<spdlog::sinks::daily_file_sink_mt>(log_path, 2, 30);
		daily_logger->set_level(spdlog::level::debug);

		std::vector<spdlog::sink_ptr> sinks{ stdout_sink, daily_logger };
		auto logger = std::make_shared<spdlog::logger>(name, sinks.begin(), sinks.end());
		//spdlog::init_thread_pool(4096, 1);
		//auto tp = std::make_shared<spdlog::details::thread_pool>(8192, 1);
		//auto logger = std::make_shared<spdlog::async_logger>(name, sinks.begin(), sinks.end(), tp, spdlog::async_overflow_policy::block);
		//auto logger = std::make_shared<spdlog::async_logger>(name, sinks.begin(), sinks.end(), spdlog::thread_pool(), spdlog::async_overflow_policy::block);

		logger->set_level(spdlog::level::info);
		logger->flush_on(spdlog::level::info);
		//logger->set_pattern("[%H:%M:%S][%^%L%$] %v");
		logger->set_pattern("%^[%H:%M:%S][%l] %v%$");
		spdlog::register_logger(logger);
	}
}

std::shared_ptr<spdlog::logger> getLog(const char * name)
{
	std::shared_ptr<spdlog::logger> log = spdlog::get(name);
	return log;
}

void setLogEnabled(IvaLogSwitch name, bool enable)
{
	logSwitchs[name] = enable;
}

bool isLogEnabled(IvaLogSwitch name)
{
	return logSwitchs[name];
}

void switchLogStatus(IvaLogSwitch name)
{
	setLogEnabled(name, !isLogEnabled(name));
}
