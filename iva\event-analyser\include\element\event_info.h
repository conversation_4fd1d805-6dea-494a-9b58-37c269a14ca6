/**
 * Project AI事件分析模块
 */


#ifndef _EVENT_INFO_H
#define _EVENT_INFO_H

#include <string>
#include "element/rect.h"
#include "element/polygon.h"
#include "enum/event_type.h"
#include "enum/target_type.h"
#include "enum/lane_type.h"
#include "enum/event_state.h"

 /**
 *
 * 事件信息
 */
namespace evt
{
	struct EventInfo {

		/**
		 * 事件ID （UUID）
		 */
		std::string id;

		/**
		 * ROI ID
		 */
		int roiID;

		/**
		 * 区域ID
		 */
		int regionID;

		/**
		 * 车道编号
		 */
		int laneID;

		/**
		 * 关联对象ID
		 */
		int targetID;

		/**
		 * 车道类型
		 */
		LaneType laneType;

		/**
		 * 关联对象类型
		 */
		TargetType targetType;

        /**
         * 目标分类类型和标签 key: classifierType value: label
         */
		std::map<std::string, std::string> targetClassTypes;

		/**
		* 事件类型
		*/
		EventType type;
		/**
		 * 事件状态
		 */
		EventState state;

		/**
		 * 事件发生时间戳
		 */
		long long occurTime;

		/**
		 * 事件移除时间戳
		 */
		long long removeTime;

		/**
		 * 事件发生位置矩形框
		 */
		Rect occurRect;

		/**
		 * 事件中有多个目标时，其他目标的矩形
		 */
		std::vector<Rect> extraRects; 
		/**
		 * 事件发生所在区域框
		 */
		Polygon occurArea;

		/**
		 * 事件所在的感兴趣区域
		 */
		Polygon roiArea;

		/**
		 * 事件是否在施工区域
		 */
		bool inConstruction = false;

		/**
		 * 平均车速  (km/h)
		 */
		float averageSpeed = 0.f;

		/**
		 * 扩展属性
		 */
		std::string ext1;
		std::string ext2;
		std::string ext3;

		//int constructionPerson;
		//int constructionVehicles;


		EventInfo() : roiID(0), regionID(0), laneID(0), targetID(0),
			type(EventType_None), state(EventState_None),
			occurTime(0), removeTime(0) {}
	};

	typedef std::vector<EventInfo> EventInfoList;
}
#endif //_EVENT_INFO_H
