#include <algorithm>
#include <chrono>
#include "opencv2/core/core.hpp"
#include <opencv2/opencv.hpp>
#include <utility>
#include "module_option.h"
#include "log.h"
#include "weather_detector.h"
#include "config/weather_config.hpp"
#include "weather_task.h"
#include "config/detect_config.hpp"


static std::map<int, std::string> WEATHER_CLASS_MAP = {{0, "大雾"}, {1, "无"}, {2, "大雨"}, {3, "大雪"}};

namespace ia
{
    using namespace std;

    class WeatherDetectorPrivate
    {
    public:
        static WeatherDetectorPrivate* getInstance(int deviceId, int threadNum)
        {
            static WeatherDetectorPrivate instance(deviceId, threadNum);
            return &instance;
        }

        explicit WeatherDetectorPrivate(int deviceID, int threadNum)
        {
            this->deviceId = deviceID;
            modelTask = std::make_shared<MODEL_TASK>(new WeatherAction(deviceId), threadNum);
        }

        void submit(cv::Mat img, void* userData, int channel)
        {
            modelTask->submit(std::move(img), userData, channel);
        }


        void setOutputCallback(int channel, function<void(CLASS_RESULT)> callback)
        {
            modelTask->setOutputCallback(std::move(callback), channel);
        }

    private:
        int deviceId = 0;
        using MODEL_TASK = ModelTask<CLASS_RESULT>;
        std::shared_ptr<MODEL_TASK> modelTask = nullptr;
    };

    void WeatherDetector::reset()
    {
        BaseDetector::reset();
        filterMap.clear();
        polyMaskInited = false;

        std::lock_guard lk(objectsLock);
        historyObjects.clear();
    }

    /**
     * @brief  设置路障跟踪模块、路障事件参数
     */
    void WeatherDetector::setParams()
    {
        matchedCountAtLeast = WEATHER_CFG->matchedCount();
        enablePostprocess = WEATHER_CFG->enablePostprocess();
        threshold = WEATHER_CFG->threshold();
        framesNum = WEATHER_CFG->cacheSize();
        detectInterval = WEATHER_CFG->inferInterval();
    }


    /**
     * 路障检测函数
     * @param frameData 当前检测帧，包含当前帧的相关信息
     */
    ImageClass WeatherDetector::detect(const FrameData& frameData)
    {
        auto detectorMgr = WeatherDetectorPrivate::getInstance(deviceId, taskNum);
        detectorMgr->setOutputCallback(frameData.channelID, [this](CLASS_RESULT inferOut){
            this->process(inferOut);
        });

        ImageClass result = {};
        if (frameData.frame.empty())
            return result;

        detectorMgr->submit(frameData.frame, nullptr, frameData.channelID);

        std::lock_guard lk(objectsLock);
        if (historyObjects.size() >= framesNum)
        {
            if (enablePostprocess)
            {
                result = postprocess();
                historyObjects.pop_front();
                return result;
            }
            else
            {
                result = historyObjects.front();
                historyObjects.pop_front();
            }
        }
        return result;
    }

    ImageClass WeatherDetector::postprocess()
    {
        if (historyObjects.empty())
            return {};

        std::unordered_map<int, std::pair<int, ImageClass>> classCountMap;  ///< [class ,[count, ImageClass]]
        for (const auto& history : historyObjects)
        {
            classCountMap[history.klass].first++;
            classCountMap[history.klass].second = history;
        }

        int maxCount = this->matchedCountAtLeast;
        ImageClass maxClass = {};
        for (const auto& [klass, countPair] : classCountMap)
        {
            if (countPair.first > maxCount)
            {
                maxCount = countPair.first;
                maxClass = countPair.second;
            }
        }

        return maxClass;
    }
    std::optional<int> getMaxKlass(std::vector<float>& results, float threshold)
    {
        auto itMax = std::max_element(results.begin(), results.end());
        if ((*itMax) > threshold)
        {
            int klass =(int)(itMax - results.begin());
            return klass;
        }
        return std::nullopt;
    }

    /**
     * @brief 推理、事件检测后处理
     * @param[in] inferOutputs 模型推理输出
     */
    void WeatherDetector::process(model_warehouse::CLASS_RESULT inferOutputs)
    {
        if (inferOutputs.empty())
            return;

        ImageClass klass;
        for (auto& imageClass : inferOutputs)
        {
            auto max = getMaxKlass(imageClass, threshold);
            if (!max.has_value())
                continue;

            if (max.value() > (imageClass.size() - 1))
            {
                IVA_LOG_ERROR("天气分类结果{} 超出标签范围 {}", max.value(), imageClass.size())
                continue;
            }

            klass.klass = max.value();
            klass.confidence = imageClass[max.value()];
            klass.label = WEATHER_CLASS_MAP[max.value()];

            std::lock_guard lk(objectsLock);
            historyObjects.emplace_back(klass);
            if (historyObjects.size() > framesNum)
                historyObjects.pop_front();
        }
    }

    void WeatherDetector::setDetectEnable(const std::map<DetectorType, bool>& detectEnable)
    {
        if (!filterMap.empty())
            return;

        for (auto& [ type, enable ] : detectEnable)
        {
            if (type == DetectorType::WeatherFog)
                filterMap[0] = enable;
            else if (type == DetectorType::WeatherRain)
                filterMap[2] = enable;
            else if (type == DetectorType::WeatherSnow)
                filterMap[3] = enable;
        }
    }

    bool WeatherDetector::parseLabelsFiles()
    {
        try {

            if (!weatherLabels.empty())
                return true;

            std::string labelsFile = DETECT_CFG->modelPath() + "/" +  WEATHER_CFG->labelsPath();

            if (!fs::exists(labelsFile) || (fs::is_empty(labelsFile)))
            {
                IVA_LOG_ERROR("\n\n\n天气标签配置文件{}不存在或者配置为空\n\n\n", labelsFile)
                return false;
            }

            std::ifstream infile(labelsFile);
            std::string lineContent;
            while (std::getline(infile, lineContent))
            {
                if (!lineContent.empty()) {
                    weatherLabels.push_back(lineContent);
                }
            }

            if (weatherLabels.empty())
            {
                IVA_LOG_ERROR("\n\n\n天气标签配置文件{} 配置错误\n\n\n" ,labelsFile)
                return false;
            }

        }
        catch (const std::exception& e) {
            IVA_LOG_ERROR("天气标签配置文件解析错误: {}", e.what())
            return false;
        }
        return true;
    }


}
