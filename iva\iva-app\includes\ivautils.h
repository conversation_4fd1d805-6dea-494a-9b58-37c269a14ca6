#ifndef IVA_UTILS_H
#define IVA_UTILS_H

#include <sys/timeb.h>
#include <sys/time.h>
#include <string>
#include <vector>
#include <chrono>
#include <ctime>
#include <iomanip>

#define WEB_SERVER_ROOT "/data/opt/tomcat/webapps"
#define WEB_SERVER_PORT 8080

#define WEB_API_DETECTINFO_REPORT "Main/api/detectInfo/report"
#define WEB_API_EVENT_SAVE "Main/api/event/save" 
#define WEB_API_EVENT_RECORD_FINISHED "Main/api/event/finishRecord"
#define WEB_API_EVENT_RELEASE "Main/api/event/release"
#define WEB_API_VIDEO_QUA_ALARM "Main/api/notification/videoQuaAlarm"
#define WEB_API_VIDEO_QUA_RECOVERY "Main/api/notification/videoQuaRecovery"
#define WEB_API_KEEP_ALIVE "Main/api/heartInfo/ivaAlive"
#define WEB_API_DETECT_STATUS "Main/api/detectInfo/detectStatus"

#define FVM_UDP_PORT 5000
#define IVA_UDP_PORT 5002

// 事件分析处理宽高
#define EVT_PROCESS_WIDTH 1920
#define EVT_PROCESS_HEIGHT 1080

// 模型目录名称
#define VEHICLE_MODEL "vehicle" // 车辆模型 目录名称
#define NON_VEHICLE_MODEL "non-vehicle" // 非车辆模型(行人|摩托车等) 目录名称
#define ROADBLOCK_MODEL "roadblock" // 路障模型 目录
#define FIRESMOKE_MODEL "firesmoke" // 烟火模型 目录
#define CAM_OFFSET_MODEL "cameraoffset" // 偏移检测模型 目录
#define THROWAWAY_MODEL "roiobject" // 抛洒物检测模型 目录
#define LANDSLIDE_MODEL "landslide" // 塌方检测模型 目录
#define WEATHER_MODEL "weather" // 天气检测模型 目录

static std::string TARGET_NAMES[] = {"person", "car", "bus", "truck", "motorcycle", "model2-person", "model2-motorcycle" };

static std::string get_system_time_str_with_ms()
{
    // 获取当前时间点
    const auto now = std::chrono::system_clock::now();
    // 转换为 time_t 类型
    const auto now_time_t = std::chrono::system_clock::to_time_t(now);
    // 转换为毫秒值（毫秒数 = 当前时间点 - 秒数时间点）
    const auto now_ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

    // 本地时间
    std::tm tm_time{};
    localtime_r(&now_time_t, &tm_time);

    // 构造时间字符串
    std::ostringstream oss;
    oss << std::put_time(&tm_time, "%Y-%m-%d %H:%M:%S");
    oss << "." << std::setfill('0') << std::setw(3) << now_ms.count();
    return oss.str();
}

// get system timestamp, milliseconds from 1970/1/1 00:00:00
static time_t get_system_timestamp()
{
    struct timeb t1;
    ftime(&t1);

    return t1.time * 1000 + t1.millitm;
}

// get time string, format: 2019-10-10 22:00:01
static std::string get_system_full_time_str(time_t ts)
{
    time_t t  = ts / 1000;
    tm t2;
    localtime_r(&t, &t2);

    return std::to_string(t2.tm_year + 1900) +
        "-" + ((t2.tm_mon + 1) < 10 ? ("0" + std::to_string(t2.tm_mon + 1)) : std::to_string(t2.tm_mon + 1)) +
        "-" + (t2.tm_mday < 10 ? ("0" + std::to_string(t2.tm_mday)) : std::to_string(t2.tm_mday)) +
        " " + (t2.tm_hour < 10 ? ("0" + std::to_string(t2.tm_hour)) : std::to_string(t2.tm_hour)) +
        ":" + (t2.tm_min < 10 ? ("0" + std::to_string(t2.tm_min)) : std::to_string(t2.tm_min)) +
        ":" + (t2.tm_sec < 10 ? ("0" + std::to_string(t2.tm_sec)) : std::to_string(t2.tm_sec));
}

// get time string, format: 20190112073020
static std::string get_system_simple_time_str(time_t ts)
{
    time_t t = ts / 1000;
    tm t2;
    localtime_r(&t, &t2);

    return std::to_string(t2.tm_year + 1900) +
           ((t2.tm_mon + 1) < 10 ? ("0" + std::to_string(t2.tm_mon + 1)) : std::to_string(t2.tm_mon + 1)) +
           (t2.tm_mday < 10 ? ("0" + std::to_string(t2.tm_mday)) : std::to_string(t2.tm_mday)) +
           (t2.tm_hour < 10 ? ("0" + std::to_string(t2.tm_hour)) : std::to_string(t2.tm_hour)) +
           (t2.tm_min < 10 ? ("0" + std::to_string(t2.tm_min)) : std::to_string(t2.tm_min)) +
           (t2.tm_sec < 10 ? ("0" + std::to_string(t2.tm_sec)) : std::to_string(t2.tm_sec));
}

// get time string, format: 20190215
static std::string get_system_short_time_str(time_t ts)
{
    time_t t = ts / 1000;
    tm t2;
    localtime_r(&t, &t2);

    return std::to_string(t2.tm_year + 1900) +
           ((t2.tm_mon + 1) < 10 ? ("0" + std::to_string(t2.tm_mon + 1)) : std::to_string(t2.tm_mon + 1)) +
           (t2.tm_mday < 10 ? ("0" + std::to_string(t2.tm_mday)) : std::to_string(t2.tm_mday));
}

// get system timestamp, usec from 1970/1/1 00:00:00
static long get_system_timestamp_of_usec()
{
    timeval time;
    gettimeofday (&time, NULL);
    return time.tv_sec * 1000000 + time.tv_usec;
}

static void split(const std::string& input, std::vector<std::string>& tokens, const std::string& delimiters = " ")
{
    auto lastPos = input.find_first_not_of(delimiters, 0);
    auto pos = input.find_first_of(delimiters, lastPos);
    while (std::string::npos != pos || std::string::npos != lastPos)
    {
        tokens.emplace_back(input.substr(lastPos, pos - lastPos));
        lastPos = input.find_first_not_of(delimiters, pos);
        pos = input.find_first_of(delimiters, lastPos);
    }
}
#endif
