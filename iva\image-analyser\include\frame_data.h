/**
 * Project IVA (image analyser)
 */
#pragma once

#include <map>
#include <vector>
#include "opencv2/core.hpp"

namespace ia
{
    /**
    * 图像目标信息
    */
    struct ImageObject
    {
        int id;

        float x;
        float y;
        float width;
        float height;

        // 分类
        int klass;
        // 置信度
        float confidence;
    };
    typedef std::vector<ImageObject> ImageObjectList;


    /**
     * 图像分类信息
     */
    struct ImageClass
    {
        // 分类
        int klass = -1;
        std::string label;
        // 置信度
        float confidence;
    };

    /**
    * 检测器类型
    */
    enum class DetectorType
    {
        // ----目标检测----
        // 烟火
        Firesmoke,
        // 路障
        Roadblock,
        // 抛洒物
        Roiobject,
        Landslide,
        Weather,
        WeatherFog,
        WeatherRain,
        WeatherSnow,

        // ----质量诊断----
        // 偏移
        Offset,
        // 大雾
        Foggy,
        // 模糊
        Blurry
    };

    /**
    * 图像帧 相关数据
    */
    struct FrameData
    {
        /*
        * 通道ID
        */
        int channelID;

        /*
        * 视频ID
        */
        int videoID;

        /*
        * 预置位ID
        */
        int presetID;

        /*
        * 图像帧 序号
        */
        int frameIndex;

        /*
        * 图像帧 宽
        */
        int width;

        /*
        * 图像帧 高
        */
        int height;

        /*
        * 车道线 (分割数据)
        */
        cv::Mat lanes;

        /*
        * 输入目标（上游已检出信息）
        */
        std::map<DetectorType, ImageObjectList> inputObjects;

        /*
        * 当前画面帧图像 (可能为空，基于部分检测器 检测频率配置)
        */
        cv::Mat frame;

    };

    /*
    * 图像帧 输出结果
    */
    struct OutputData
    {
        /*
        * 图像状态（VQD）
        */
        std::map<DetectorType, bool> frameState;

        /*
        * 图像帧 输出目标集（烟火、抛洒物等）
        */
        std::map<DetectorType, ImageObjectList> outputObjects;
        /**
         * 图像帧 输出分类
         */
        std::map<DetectorType, ImageClass> outputClass;
    };

    typedef std::vector<std::vector<float>> RegionPositions;
    typedef std::vector<std::vector<cv::Point2d>> RegionPoints;
}
