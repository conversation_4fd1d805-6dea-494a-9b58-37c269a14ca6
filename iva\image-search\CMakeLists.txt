cmake_minimum_required (VERSION 3.5.2)

## 目标生成 Image search service
set(APP_NAME "iss-app")
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${PROJECT_SOURCE_DIR}/out)
set(LIB_IVA ${PROJECT_SOURCE_DIR}/out/lib)

add_compile_options(-std=c++17 -Wno-deprecated-declarations)

# boost
set(BOOST_HOME "/opt/boost")

# TensorRT
set(TensorRT_HOME "/usr/local/TensorRT")
set(TensorRT_LIB
    nvinfer
	nvinfer_plugin
)

# CUDA
set(CUDA_HOME "/usr/local/cuda")
set(CUDA_LIB
    cudart
    cuda
)

# opencv
set(OPENCV_HOME "/usr/local")
set(OPENCV_LIB
    opencv_core
    opencv_imgproc
    opencv_imgcodecs
)

# 头文件
include_directories(
	${BOOST_HOME}/include/
	${OPENCV_HOME}/include/
    ${TensorRT_HOME}/include/
    ${CUDA_HOME}/include/
    ${PROJECT_SOURCE_DIR}/iva-log
    ${PROJECT_SOURCE_DIR}/common
    ${PROJECT_SOURCE_DIR}/network
    ${PROJECT_SOURCE_DIR}/network/include
    ${PROJECT_SOURCE_DIR}/image-analyser/include
    ${PROJECT_SOURCE_DIR}/image-analyser/
    ${PROJECT_SOURCE_DIR}/image-analyser/model_warehouse/nvidia
)

# 库路径
link_directories(
	${OPENCV_HOME}/lib/
    ${TensorRT_HOME}/lib/
    ${CUDA_HOME}/lib64/
    #${LIB_IVA}
)

# 当前文件夹cpp
FILE(GLOB src "*.cpp")
FILE(GLOB image_analyser "../image-analyser/model_warehouse/nvidia/*.cpp")
FILE(GLOB util "../image-analyser/util/algorithm_util.cpp" "../network/util/utility.cpp")
FILE(GLOB log "../iva-log/*.cpp")
SET(ALL_SRC ${image_analyser} ${util} ${log} ${src})

# 生成iva-app
add_executable(${APP_NAME} ${ALL_SRC})

target_link_libraries(${APP_NAME} ${TensorRT_LIB} ${CUDA_LIB} ${OPENCV_LIB} pthread stdc++fs)

#SET(CMAKE_BUILD_WITH_INSTALL_RPATH TRUE)
#set_target_properties(${APP_NAME} PROPERTIES INSTALL_RPATH "./lib/")

