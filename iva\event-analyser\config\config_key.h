/**
 * Project AI事件分析模块
 */

#ifndef _CONFIGKEYNAME_H
#define _CONFIGKEYNAME_H

#include <string>

 /**
  * 配置项 定义
  */
namespace evt
{
	using std::string;

	///////////////////////// 通用 //////////////////////////////
	/**
	* 机动车最大尺寸 0.4
	*/
	const string MAX_SIZE_VEHICLE = "MaxSizeVehicle";

	/**
	* 机动车最小尺寸 0.03
	*/
	const string MIN_SIZE_VEHICLE = "MinSizeVehicle";

	/**
	 * 行人最大尺寸 0.2
	 */
	const string MAX_SIZE_PEDSTRAIN = "MaxSizePedstrain";

	/**
	 * 行人最小尺寸 0.02
	 */
	const string MIN_SIZE_PEDSTRAIN = "MinSizePedstrain";

	/**
	 * 全局停车行人 相似缓存时间 1800
	 */
	const string GLOBAL_CACHE_TIME = "GlobalCacheTime";

	/**
	 * 目标注册点X
	 */
	const string TARGET_REGIST_X = "TargetRegistX";

	/**
	 * 目标注册点Y
	 */
	const string TARGET_REGIST_Y = "TargetRegistY";

	///////////////////////// 停车 //////////////////////////////
	/**
	 * 停车候选时间 默认0.8s
	 */
	const string STOP_PROPOSE_TIME = "StopProposeTime";

	/**
	 * 停车检测时长(秒) 5
	 */
	const string STOP_CHECK_TIME = "StopCheckTime";

	/**
	 * 停车移除时长(秒) 60
	 */
	const string STOP_REMOVE_TIME = "StopRemoveTime";

	/**
	 * 停车检测IOU 0.9
	 */
	const string STOP_CHECK_IOU = "StopCheckIou";

	/**
	 * 停车检查通过比率 0.5
	 */
	const string STOP_CHECK_RATE = "StopCheckRate";

	/**
	 * 停车事件相似检查距离间隔（像素） 10
	 */
	const string STOP_EVENT_SPACE = "StopEventSpace";

	/**
	 * 拥堵后撤回停车事件的判断时间 60
	 */
	const string STOP_WITHDRAW_JAM = "StopWithdrawJam";



	///////////////////////// 行人 //////////////////////////////
	/**
	 * 行人候选时间 默认0.5s
	 */
	const string PEDSTRAIN_PROPOSE_TIME = "PedstrainProposeTime";

	/**
	 * 行人检测时长(秒) 2
	 */
	const string PEDSTRAIN_CHECK_TIME = "PedstrainCheckTime";

	/**
	 * 行人移除时长(秒) 60
	 */
	const string PEDSTRAIN_REMOVE_TIME = "PedstrainRemoveTime";

	/**
	 * 行人检测通过比率 0.2
	 */
	const string PEDSTRAIN_CHECK_RATE = "PedstrainCheckRate";

	/**
	 * 行人事件相似检查距离间隔（像素） 100
	 */
	const string PEDSTRAIN_EVENT_SPACE = "PedstrainEventSpace";

	/**
	 * 行人事件最大速度（像素） 8
	 */
	const string PEDSTRAIN_MAX_SPEED = "PedstrainMaxSpeed";

	///**
	// * 行人最低高宽比 1.4
	// */
	const string PEDSTRAIN_MIN_HW_RATIO= "PedstrainMinHWRatio";

	///////////////////////// 摩托车 //////////////////////////////
	/**
	 * 摩托车检测时长(秒) 2
	 */
	const string TWOWHEELS_CHECK_TIME = "TwoWheelsCheckTime";

	/**
	 * 摩托车移除时间(秒) 60
	 */
	const string TWOWHEELS_REMOVE_TIME = "TwoWheelsRemoveTime";

	/**
	 * 摩托车最低车速 20km/h
	 */
	const string TWOWHEELS_MIN_SPEED = "TwoWheelsMinSpeed";

	/**
	 * 摩托车事件相似检查距离间隔（像素） 100
	 */
	const string TWOWHEELS_EVENT_SPACE = "TwoWheelsEventSpace";



	///////////////////////// 逆行 //////////////////////////////
	/**
	 * 逆行检测角度 140
	 */
	const string OPPOSITE_ANGLE = "OppositeAngle";

	/**
	 * 逆行移除时长(秒) 120
	 */
	const string OPPOSITE_REMOVE_TIME = "OppositeRemoveTime";

	/**
	 * 逆行检测参考距离 像素 100
	 */
	const string OPPOSITE_DISTANCE = "OppositeDistance";

	/**
	 * 逆行事件相似检查距离间隔（像素） 10
	 */
	const string OPPOSITE_EVENT_SPACE = "OppositeEventSpace";

	/**
	 * 逆行事件检查满足率
	 */
	const string OPPOSITE_CHECK_RATE = "OppositeCheckRate";

	/**
	 * 逆行车辆最小尺寸
	 */
	const string OPPOSITE_MIN_SIZE = "OppositeMinSize";

	/**
	 * 偏移后撤回逆行事件的判断时间 60
	 */
	const string OPPOSITE_WITHDRAW = "OppositeWithdraw";


	///////////////////////// 驶入 //////////////////////////////
	/**
	 * 驶入检测时长(秒) 0.24
	 */
	const string DRIVE_IN_CHECK_TIME = "DriveInCheckTime";

	/**
	 * 驶入移除时长(秒) 60
	 */
	const string DRIVE_IN_REMOVE_TIME = "DriveInRemoveTime";

	/**
	 * 驶入事件相似检查距离间隔（像素） 50
	 */
	const string DRIVE_IN_EVENT_SPACE = "DriveInEventSpace";



	///////////////////////// 驶离 //////////////////////////////
	/**
	 * 驶离检测时长(秒) 0.5
	 */
	const string DRIVE_AWAY_CHECK_TIME = "DriveAwayCheckTime";

	/**
	 * 驶离移除时长(秒) 60
	 */
	const string DRIVE_AWAY_REMOVE_TIME = "DriveAwayRemoveTime";

	/**
	 * 驶离事件相似检查距离间隔（像素） 50
	 */
	const string DRIVE_AWAY_EVENT_SPACE = "DriveAwayEventSpace";



	///////////////////////// 变道 //////////////////////////////
	/**
	 * 变道检测时长(秒) 0.5
	 */
	const string DRIVE_ACROSS_CHECK_TIME = "DriveAcrossCheckTime";

	/**
	 * 变道检测过线参考距离(像素) 50
	 */
	const string DRIVE_ACROSS_DISTANCE = "DriveAcrossDistance";

	/**
	 * 变道移除时长(秒) 60
	 */
	const string DRIVE_ACROSS_REMOVE_TIME = "DriveAcrossRemoveTime";

	/**
	 * 变道事件相似检查距离间隔（像素） 50
	 */
	const string DRIVE_ACROSS_EVENT_SPACE = "DriveAcrossEventSpace";



	///////////////////////// 拥堵 //////////////////////////////
	/**
	 * 拥堵检测时长(秒) 3
	 */
	const string JAM_CHECK_TIME = "JamCheckTime";

	/**
	 * 拥堵生效慢行数量 6
	 */
	const string JAM_START_SLOW_COUNT = "JamStartSlowCount";

	/**
	 * 拥堵生效占有率 0.3
	 */
	const string JAM_START_OCCUPIED_RATIO = "JamStartOccupiedRatio";

	/**
	 * 拥堵解除占有率 0.1
	 */
	const string JAM_REMOVE_OCCUPIED_RATIO = "JamRemoveOccupiedRatio";

	/**
	 * 拥堵解除正常车速比率 0.5
	 */
	const string JAM_REMOVE_NORMAL_RATIO = "JamRemoveNormalRatio";

	/**
	 * 拥堵移除时长(秒）300
	 */
	const string JAM_REMOVE_TIME = "JamRemoveTime";

	/**
	 * 拥堵平均速度(Km/h）20
	 */
	const string JAM_MEAN_SPEED = "JamMeanSpeed";

	/**
	 * 参与拥堵计算车辆最小像素 30
	 */
	const string SLOW_VEHICLE_MIN_SIZE = "SlowVehicleMinSize";


	///////////////////////// 抛洒物 //////////////////////////////
	/**
	 * 抛洒物移除时间
	 */
	const string OBSTACLE_REMOVE_TIME = "ObstacleRemoveTime";

	///////////////////////// 路障 //////////////////////////////
	/**
	 * 路障移除时间
	 */
	const string ROADBLOCK_REMOVE_TIME = "RoadblockRemoveTime";

	///////////////////////// 烟火 //////////////////////////////
	/**
	 * 烟火移除时间
	 */
	const string FIRESMOKE_REMOVE_TIME = "FiresmokeRemoveTime";

    ///////////////////////// 塌方 //////////////////////////////
    /**
     * 塌方移除时间
     */
    const string LANDSLIDE_REMOVE_TIME = "LandslideRemoveTime";

    ///////////////////////// 天气 //////////////////////////////
    /**
     * 大雾移除时间
     */
    const string FOG_REMOVE_TIME = "FogRemoveTime";

    /**
     * 大雨移除时间
     */
    const string RAIN_REMOVE_TIME = "RainRemoveTime";

    /**
     * 大雪移除时间
     */
    const string SNOW_REMOVE_TIME = "SnowRemoveTime";

	///////////////////////// 施工 //////////////////////////////
	/**
	 * 施工检测时长(秒) 3
	 */
	const string ROAD_CONSTRUCTION_CHECK_TIME = "RoadConstructCheckTime";

	/**
	 * 施工移除时间(秒)
	 */
	const string ROAD_CONSTRUCTION_REMOVE_TIME = "RoadConstructRemoveTime";

	/**
	 * 施工生效最少行人数量 1
	 */
	const string ROAD_CONSTRUCTION_PED_NUM = "RoadConstructPedNum";

	/**
	 * 施工生效最少路障数量 2
	 */
	const string ROAD_CONSTRUCTION_BLOCK_NUM = "RoadConstructBlockNum";

	/**
	 * 施工生效最少停车数量 1
	 */
	const string ROAD_CONSTRUCTION_STOP_NUM = "RoadConstructStopNum";

	///////////////////////// 事故 //////////////////////////////
	/**
	 * 事故检测时长(秒) 2
	 */
	const string ACCIDENT_CHECK_TIME = "AccidentCheckTime";

	/**
	 * 事故生效停车数量 2
	 */
	const string ACCIDENT_STOP_COUNT = "AccidentStopCount";

	/**
	 * 事故点检测周边车辆范围 300
	 */
	const string ACCIDENT_CHECK_DISTANCE = "AccidentCheckDistance";


	/**
	 * 事故移除时长(秒）300
	 */
	const string ACCIDENT_REMOVE_TIME = "AccidentRemoveTime";

}
#endif //_CONFIGKEYNAME_H