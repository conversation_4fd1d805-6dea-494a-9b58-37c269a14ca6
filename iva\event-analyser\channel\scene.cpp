/**
 * Project AI事件分析模块
 */

#include <iostream>
#include "scene.h"
#include "util/scene_utility.h"
#include "config/global_region_config.h"
#include "config/event_config.hpp"

 /**
  * Scene implementation
  *
  * 通道事件分析场景
  */
namespace evt
{

    // 过线车辆正常像素速度
    #define VEHICLE_NORMAL_MIN_SPEED 3.f

	Scene::Scene(int id) :
		frameIndex(0),
		frameRate(DEFAULT_FRAME_RATE),
		frameWidth(DEFAULT_FRAME_WIDTH),
		frameHeight(DEFAULT_FRAME_HEIGHT),
		eventMap(NULL),
		targetMap(NULL),
		detectState(DetectState_Paused){
		this->channelID = id;
	}

	Scene::~Scene() {
		release();
	}

	/**
	 * 初始化感兴趣区配置
	 * @param roicfg
	 * @param planLevel 参数预案等级
	 */
	void Scene::init(vector<ROIInfo>& roicfgs, int planLevel) {

		std::lock_guard<std::mutex> m(sceneMutex);

		this->paramPlanLevel = planLevel;
		G_REGION_CFG->getValue<int>(stopWithdrawJamTime, STOP_WITHDRAW_JAM, planLevel);
		G_REGION_CFG->getValue<int>(oppsiteWithdrawTime, OPPOSITE_WITHDRAW, planLevel);

		if (!roiList.empty())
		{
			for (auto& roi : roiList)
			{
				delete roi;
			}
			roiList.clear();
		}

		for (auto& roiInfo : roicfgs)
		{
			auto roi = new ROI(roiInfo);
			roiList.push_back(roi);
			roi->setTargetMap(targetMap);
			roi->setEventMap(eventMap);
			roi->setFrameRate(frameRate);
			roi->setSceneState(&sceneState);
		}
		sceneState.init(roiList);
	}

	/**
	 * 暂停检测
	 */
	void Scene::pause(bool camOffset)
	{
		detectState = DetectState_Paused;
		if (camOffset)
		{
			std::lock_guard<std::mutex> m(sceneMutex);
			withdrawEventsByType(EventType_Opposite, EVENT_CFG->camOffsetWithdrawTime(), WithdrawReason_CameraOffset);

			for (auto& roi : roiList)
			{
				roi->setOffsetState(true);
			}
		}
	}

	/**
	 * 恢复检测
	 */
	void Scene::resume()
	{
		detectState = DetectState_Resumed;
		for (auto& roi : roiList)
		{
			roi->setOffsetState(false);
		}
	}

	/**
	 * 设置画面尺寸
	 * @param w 宽
	 * @param h 高
	 */
	void Scene::setFrameSize(int w, int h)
	{
		std::lock_guard<std::mutex> m(sceneMutex);
		frameWidth = w;
		frameHeight = h;

		if (eventMap == NULL && targetMap == NULL)
		{
			int cacheTime = 0;
			G_REGION_CFG->getValue<int>(cacheTime, GLOBAL_CACHE_TIME, paramPlanLevel);
			eventMap = new EventMap(frameWidth, frameHeight, cacheTime);
			targetMap = new TargetMap(frameWidth, frameHeight);
			for (auto& roi : roiList)
			{
				roi->setTargetMap(targetMap);
				roi->setEventMap(eventMap);
				roi->setFrameSize(w, h);
			}
		}
	}

	/**
	 * 设置画面帧率
	 * @param rate 帧率
	 */
	void Scene::setFrameRate(int rate)
	{
		frameRate = rate;
		for (auto& roi : roiList)
		{
			roi->setFrameRate(rate);
		}
	}

	/**
	 * 更新轨迹（新增或更新目标）
	 * @param tracks
	 */
	bool Scene::update(TrackList& tracks, long frameIndex) {

		std::lock_guard<std::mutex> m(sceneMutex);
		if (eventMap == NULL || targetMap == NULL)
		{
			std::cerr << "Scene frame size not set" << std::endl;
			return false;
		}
		this->frameIndex = frameIndex;
		if (detectState != DetectState_Running)
		{
			detectState = DetectState_Running;
			startTime = steady_clock::now();

			for (auto& roi : roiList)
			{
				roi->setStartDetectTime(startTime);
			}
		}

		/************************ 预处理 ********************/
		targetMap->newFrameStart();
		for (auto& target : targetList)
		{
			target->life--;
			target->tracked = false;
			target->info.showable = false;
			target->updateArea(NULL, false, false);
		}
		for (auto& roi : roiList)
		{
			roi->clearTargets();
			roi->setJamState(sceneState.inJam(roi->getID()));
		}
		eventMap->update();

		// 关联目标
		sceneState.onUpdateStart();
		passedTargetLists.clear();
        passedVehicleLists.clear();
		for (auto& track : tracks)
		{
			auto target = matchTarget(track);
			if (target != NULL && target->life >= 0)
			{
				target->tracked = true;
				target->life++;
				target->life = std::min(TARGET_MAX_LIFE, target->life);

				// 关联ROI
				auto matchedInfo = matchROI(target);
				auto roi = std::get<0>(matchedInfo);
				auto area = std::get<1>(matchedInfo);

				if (roi != NULL)
				{
					// 计数线判断
					updateCountLine(target, roi);
#ifdef USE_SEARCH_MODULE
                    capturePassedVehicle(target,roi);
#endif

					// 更新场景状态
					sceneState.onUpdate(target, roi);

					// 更新目标地图
					if (target->getTrackCount() <= 1)
					{
						targetList.push_back(target);
						targetMap->addTarget(target, roi);
					}
					else
					{
						targetMap->updateTarget(target, roi);
					}
				}
			}
		}
		sceneState.onUpdateEnd();

		// 目标过线通知
		if (passedTargetLists.size() > 0)
            targetsPassedCallback(passedTargetLists);

#ifdef USE_SEARCH_MODULE
        if (!passedVehicleLists.empty())
            vehiclesPassedCallback(passedVehicleLists);
#endif
		if (vehiclesInfoCallback)
		{
			const auto now = std::chrono::steady_clock::now();
			if (now - lastReportTime > std::chrono::seconds (EVENT_CFG->vehicleInfoReportInterval()))
			{
				std::vector<evt::TargetInfo> targets;
				for (const auto& t : targetList)
					targets.emplace_back(t->getTargetInfo());
				vehiclesInfoCallback(targets);
				lastReportTime = now;
			}
		}

		// 移除 未匹配目标
		targetList.remove_if([&](TargetPtr t)
		{
			if (t->life <= 0)
			{
				t->removed = true;
				targetMap->delTarget(t);
				return true;
			}
			return false;
		});

		/************************ 事件分析 ********************/
		auto passedSeconds = std::chrono::duration_cast<std::chrono::seconds>(steady_clock::now() - startTime).count();
		if(passedSeconds >= EVENT_CFG->checkDelay())
			checkEvents();

		/************************ 后处理 ********************/
		postProcessEvents();

		/************************ 清除其他检测对象 ********************/
		for (auto& roi : roiList)
		{
			roi->clearEventObjects();
		}
		return true;
	}

	/**
	 * 更新场景事件物体信息 （抛洒物、烟火、路障等）
	 * @param objs 物体列表
	 */
	void Scene::update(EventObjectList& objs)
	{
		std::lock_guard<std::mutex> m(sceneMutex);
		for (auto& evtObj : objs)
		{
			for (auto& roi : roiList)
			{
				if (roi->matchEventObjects(evtObj))
					break;
			}
		}
	}

	/**
	 * 获取当前所有的目标集合
	 */
	void  Scene::getCurrentTargets(TargetInfoList& outputTargets) {
		std::lock_guard<std::mutex> m(sceneMutex);
		for (auto& t : targetList)
		{
			if (t->tracked)
			{
				outputTargets.push_back(t->info);
			}
		}
	}

	/**
	 * 匹配目标
	 * @param track 轨迹框
	 */
	TargetPtr Scene::matchTarget(Track & track) {

		auto target = getTargetById(track.id);
		if (target == NULL)
		{
			target = std::make_shared<Target>(track.id, track.type, track.targetClassTypes);
			target->updateTrack(track);
		}
		else
		{
			// 更新轨迹
            target->updateClassType(track.targetClassTypes);
			target->updateTrack(track);
		}
		return target;
	}

	/**
	 * 基于ID 获取目标
	 */
	TargetPtr Scene::getTargetById(int id)
	{
		TargetPtr target = NULL;
		auto i = targetList.begin();
		while (i != targetList.end())
		{
			if ((*i)->getID() == id)
			{
				target = *i;
				break;
			}
			i++;
		}
		return target;
	}

	/**
	 * 匹配更新目标ROI及区域信息
	 * @param target
	 * @return ROI*
	 */
	std::tuple<ROI*, Area*> Scene::matchROI(TargetPtr target) {

		for (auto& roi : roiList)
		{
			if (auto area = roi->matchArea(target))
				return std::make_tuple(roi, area);
		}
		target->updateArea( 0, false, false );
		return std::make_tuple(nullptr, nullptr);
	}

	/**
	 * 更新目标过线状态
	 * @param target
	 */
	void Scene::updateCountLine(TargetPtr target, ROI* roi) {

		auto lastLineState = target->getCountLineState();
		roi->updateCountLineState(target);
		auto lineState = target->getCountLineState();

		if (lastLineState != TargetCountLineState_None && lastLineState != TargetCountLineState_Finished)
		{
			if (lineState != lastLineState) //目标过线时，开始准备计算速度
				target->startCountSpeed(); 
			if ( target->needCountSpeed() )  //目标可以计算速度
			{
				if (sceneState.inJam(roi->getID()))	    ///< 拥堵情况下则判断过线累计轨迹长度
				{
					Vector2D displacement(target->getLatestPos(target->getTrackCount() - 1), target->getLatestPos(0));
					if (displacement.length() < TARGET_ACCUMULATED_DISPL_MIN) return;
				}
				// 计算速度
				roi->updateSpeed(target);
				target->info.leaveTime = systemTimestamp();
				passedTargetLists.push_back(target->info);
				target->updateCountLineState(TargetCountLineState_Finished);
				//if ( roi->getID() == 74 )
				//IVA_LOG_INFO("ROI {} TARGET {} type: {} speed: {} ", roi->getID(), target->getID(), target->getType(),
				//	target->getSpeed() );
			}
		}
	}

    /**
     * 捕获清晰的过线车辆
     * @param target
     */
    void Scene::capturePassedVehicle(TargetPtr target, ROI* roi)
    {
        if (!target || !roi || !target->isVehicleType() || target->vehicleCaptured)
            return;

        Rect rect = target->getLatestRect();
		if (!roi->isEnableRetrieveFeature(rect))
			return;

		auto [x, y, width, height] = rect;
		float bottomY = y + height;
        float rightX = x + width;

        //! 由北向南方向的则判断速度，否则不判断，并且放宽出边界的像素距离
        bool fromNorth2South = false;
        float extraDistance = 0.f;
        Vector2D north2SouthDirection({0,0} , {0, (float)frameHeight});
        if (roi->getDirection().angle(north2SouthDirection) < 90)
            fromNorth2South = true;
        else
            extraDistance = 80;

        //! 目标距离图像两侧边界的最大最小阈值
        float minSideDistance = EVENT_CFG->minPassedBorderDistance();
        float maxSideDistance = EVENT_CFG->maxPassedBorderDistance() + extraDistance;

        //! 目标距离图像底部边界的最大最小阈值
        float minBottomDistance = minSideDistance;
        float maxBottomDistance = maxSideDistance;

        auto featureRoiBottom = roi->getFeatureRoiBottom();
        if (featureRoiBottom.has_value())
        {
            //! 如果feature ROI底边距图像边界距离小于设置的最小像素距离，以设置值作为最小距离。否则以feature ROI底边距图像边界距离作为最小距离
            minBottomDistance = (float)std::max(frameHeight - (int)featureRoiBottom.value(), EVENT_CFG->minPassedBorderDistance());
            //! 如果由南向北驶入边界，则放宽距离。 如果以feature ROI底边距图像边界距离作为最小距离，则最大距离为 设置的最小值与设置的最大值差值差值 加上 minBorderDistance
            maxBottomDistance = (float)(EVENT_CFG->maxPassedBorderDistance() - EVENT_CFG->minPassedBorderDistance()) + minBottomDistance + extraDistance;
        }

        //! 边界距离判断
        if ( ((x <= maxSideDistance && x > minSideDistance) && (bottomY < (float)frameHeight - minBottomDistance)) //!< 车从左边出图像区域 左边框距离左边界
             || (((bottomY >= (float)frameHeight - maxBottomDistance) && (bottomY <= (float)frameHeight - minBottomDistance)) && (x > maxSideDistance) && (rightX < (float)frameWidth - maxSideDistance)) //!< 车从底部出图像区域
             || ((rightX >= (float)frameWidth - maxSideDistance) && (rightX <= (float)frameWidth - minSideDistance) && (bottomY < (float)frameHeight - minBottomDistance)) //!< 车从右边出图像区域 右边框距离右边界
                )
        {
            if (fromNorth2South)
            {
                //! 计算像素速度（按车框比率）
                auto velocity = target->getVelocity();
                float speed = velocity.length();
                float speedRatio = speed / width;

                //! 速度过滤
                if (speedRatio* 100.f < VEHICLE_NORMAL_MIN_SPEED)
                    return;
            }

            if (sceneState.inJam(roi->getID()))	    //!< 拥堵情况下则判断过线累计轨迹长度
            {
                Vector2D displacement(target->getLatestPos(target->getTrackCount() - 1), target->getLatestPos(0));
                if (displacement.length() < TARGET_ACCUMULATED_DISPL_MIN)
                    return;
            }
            passedVehicleLists.emplace_back(target->info);
            target->vehicleCaptured = true;
        }
    }

	/**
	 * 检查事件
	 */
	void Scene::checkEvents() {
		for (auto& roi : roiList)
		{
			roi->detectEvents();
		}
	}

	/**
	 * 后处理事件
	 */
	void Scene::postProcessEvents() {

		EventList confirmedEvents;
		EventList removedEvents;

		auto allEvents = eventMap->typeEvents;
		for (auto& pair : allEvents)
		{
			auto evtType = pair.first;
			auto& evtList = pair.second;
			for (auto& evt : evtList)
			{
				auto evtState = evt->getState();
				switch (evtState)
				{
				case evt::EventState_None:
					eventMap->delEvent(evt);
					break;
				case evt::EventState_Confirming:

					if (evtType == EventType_Jam)		// 拥堵状态更新
					{
						sceneState.onUpdateJam(evt);
						for (auto& evt : confirmedEvents)
						{
							if (evt->getType() == EventType_Stop && sceneState.inJam(evt->getROIID()))
							{
								if (EVENT_CFG->ignoreEmergencyStopWhenJam() || evt->info.laneType != LaneType_Emergency)
								{
									evt->ignored = true;
								}
							}
						}
					}
					else if (evtType == EventType_Stop)		// 停车过滤
					{
						if (sceneState.inJam(evt->getROIID()) && (EVENT_CFG->ignoreEmergencyStopWhenJam() || evt->info.laneType != LaneType_Emergency))
						{
							evt->ignored = true;
						}
						else
						{
							eventMap->checkCacheEvent(evt);
						}
					}
					else if (evtType == EventType_Pedstrain)// 行人过滤
					{
						eventMap->checkCacheEvent(evt);
					}
					else if (evtType == EventType_Opposite) // 逆行过滤
					{
						if(sceneState.isShifted())
							evt->ignored = true;
					}

					// 交通流偏移 若配置撤回 
					if (EVENT_CFG->ignoreAllWhenShifted() && sceneState.isShifted() && canIgnoreWhenShifted(evtType))
					{
						evt->ignored = true;
						IVA_LOG_INFO("Channel:{} [event ignored] since traffic flow shifted! roi {} id {} type {}", channelID, evt->getROIID(), evt->getID(), evt->getType());
					}
					confirmedEvents.push_back(evt);
					break;

				case evt::EventState_Removed:
					// 拥堵状态更新
					if (evtType == EventType_Jam)
					{
						sceneState.onUpdateJam(evt);
					}
					removedEvents.push_back(evt);
					break;
				default:
					break;
				}
			}
		}

		// 确认通过事件 上报
		for (auto& evt : confirmedEvents)
		{
			if (!evt->ignored )  
			{
				evt->setState(EventState::EventState_Maintaining);

				//处理撤回事件
				dealWithdrawEvent(evt);
 				newEvtCallback(evt->info);
			}
			else
			{
				if(evt->getType() == EventType_Stop && sceneState.inJam(evt->getROIID()))
					evt->setState(EventState_None);
				else
					evt->setState(EventState::EventState_Maintaining);
			}
		}

		// 移除事件  上报
		for (auto& evt : removedEvents)
		{
			evt->setRemoveTime(systemTimestamp());
			eventMap->delEvent(evt);
			if (!evt->ignored)
				newEvtCallback(evt->info);
		}

		// 如果偏移，撤回指定时间内的逆行事件
		if (sceneState.isShifting() && oppsiteWithdrawTime > 0)
		{
			withdrawEventsByType(EventType_Opposite, oppsiteWithdrawTime, WithdrawReason_TrafficFlow);
		}
	}

	/**
	 * 撤回指定类型事件
	 * @param t 事件类型
	 * @param withdrawTime 撤回时间
	 * @param reason 撤回原因
	 * @param roiID 指定ROI
	 */
	void Scene::withdrawEventsByType(EventType t, int withdrawTime, WithdrawReason reason, int roiID)
	{
		if (eventMap == NULL)
			return;

		long long tmNow = systemTimestamp();
		auto listEvent = eventMap->getWithdrawEvent(t, tmNow - withdrawTime * 1000, roiID);
		if (!listEvent.empty())
		{
			for (auto& ei : listEvent)
			{
				withdrawEvtCallback(ei);
				auto evtptr = eventMap->getEventByInfo(ei);
				if (evtptr != NULL)
				{
					evtptr->ignored = true;
					if (t == EventType_Stop)
						evtptr->setState(EventState_None);
				}
				IVA_LOG_WARN("Channel:{} [withdraw event] id: {} type: {} reason: {}", channelID, ei.id, ei.type, reason);
			}
		}
	}


	/**
	 * 处理可撤回事件
	 * @param evt 事件
	 */
	void Scene::dealWithdrawEvent(EventPtr evt)
	{		
		long long tmNow = systemTimestamp();
		EventType eType = evt->getType();
		if (eType == EventType_Jam && stopWithdrawJamTime > 0 )  //拥堵时撤回停车事件
		{
			withdrawEventsByType(EventType_Stop, stopWithdrawJamTime, WithdrawReason_Jam, evt->getROIID());
		}
		else if (eType == EventType_Stop && stopWithdrawJamTime > 0 )  //停车事件加入到可撤回队列中
		{
			if (EVENT_CFG->ignoreEmergencyStopWhenJam() || evt->info.laneType != LaneType_Emergency)
				eventMap->addWithdrawEvent(eType, tmNow - stopWithdrawJamTime * 1000, evt);
		}
		else if (eType == EventType_Opposite && oppsiteWithdrawTime > 0 )  //逆行事件加入到可撤回队列中
		{
			int oppositeCacheTime = std::max(EVENT_CFG->camOffsetWithdrawTime(), oppsiteWithdrawTime);
			eventMap->addWithdrawEvent(eType, tmNow - oppositeCacheTime * 1000, evt);
		}
	}

	/**
	 * 设置发生新事件回调
	 * @param cb
	 */
	void Scene::setNewEvtCallback(EventCallback cb) {
		newEvtCallback = cb;
	}

	/**
	 * 设置事件撤回回调
	 * @param cb
	 */
	void Scene::setWithdrawEvtCallback(EventCallback cb)
	{
		withdrawEvtCallback = cb;
	}

	/**
	 * 设置过线目标回调
	 * @param cb
	 */
	void Scene::setTargetsPassedCallback(TargetsPassedCallback cb) {
		targetsPassedCallback = cb;
	}

    /**
     * 设置车辆过线回调
     * @param cb
     */
    void Scene::setVehiclesPassedCallback(TargetsPassedCallback cb) {
        vehiclesPassedCallback = cb;
    }

	/**
	 * 获取当前事件信息
	 * @outputEvents 输出事件信息集合
	 * @evtType 查找类型
	 */
	void Scene::getCurrentEventInfos(EventInfoList& outputEvents, EventType evtType)
	{
		outputEvents.clear();
		std::lock_guard<std::mutex> m(sceneMutex);
		if (eventMap != NULL)
		{
			auto typeEvts = eventMap->getEventsByType(evtType);
			for (auto& e : typeEvts)
			{
				outputEvents.push_back(e->info);
			}
		}
	}

	/**
	 * 释放资源
	 */
	void Scene::release()
	{
		std::lock_guard<std::mutex> m(sceneMutex);
		for (auto& roi : roiList)
		{
			delete roi;
		}
		roiList.clear();

		targetList.clear();
		passedTargetLists.clear();

		if (eventMap != NULL) delete eventMap;
		if (targetMap != NULL) delete targetMap;
	}
}
