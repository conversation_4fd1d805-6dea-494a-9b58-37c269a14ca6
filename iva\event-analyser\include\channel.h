/**
 * Project AI事件分析模块
 */

#ifndef _EVT_CHANNEL_H
#define _EVT_CHANNEL_H

#include <functional>
#include <mutex>
#include "module_def.h"
#include "area/roi_info.h"
#include "element/target_info.h" 
#include "element/event_object.h" 
#include "element/event_info.h"

/**
* 分析通道
*
* 每个通道至少包含一个分析场景（对应一个预置位）
*/
namespace evt
{
	typedef std::function<void(EventInfo)> EventCallback;
	typedef std::function<void(TargetInfoList&)> TargetsPassedCallback;
	typedef std::function<void(std::vector<TargetInfo>&)> VehiclesInfoCallback;

	class Scene;
	class Channel {
	public:

		Channel(int id);
		~Channel();

		/**
		 * 初始化感兴趣区配置
		 * @param roicfg
		 * @param evtWidth,evtHight 事件分析处理宽高
		 * @param presetID 预置位ID
		 * @param planLevel 参数预案等级
		 */
		EVT_EXPORT void init(ROIInfoList& roicfgs, int evtWidth, int evtHight, int presetID = 0, int planLevel= 1);

		/**
		 * 更新轨迹（新增或更新目标）
		 * @param tracks 轨迹框列表
		 * @param frameIndex 检测帧序号
		 * @presetID 预置位ID
		 */
		EVT_EXPORT bool update(TrackList& tracks, long frameIndex = 0, int presetID = 0);

		/**
		 * 更新场景事件物体信息 （抛洒物、烟火、路障等）
		 * @param eventObjects 事件物体列表
		 * @presetID 预置位ID
		 */
		EVT_EXPORT void update(EventObjectList& eventObjects, int presetID = 0);

		/**
		 * 暂停检测
		 * @presetID 预置位ID
		 * @camOffset 是否摄像机偏移暂停
		 */
		EVT_EXPORT void pause(int presetID = 0, bool camOffset = false);

		/**
		 * 恢复检测
		 * @presetID 预置位ID
		 */
		EVT_EXPORT void resume(int presetID = 0);

		/**
		 * 设置画面尺寸
		 * @param w 宽
		 * @param h 高
		 */
		EVT_EXPORT void setFrameSize(int w, int h);

		/**
		 * 设置画面帧率
		 * @param rate 帧率
		 */
		EVT_EXPORT void setFrameRate(int rate);

		/**
		 * 获取当前所有的目标集合
		 * @outputTargets 输出目标集合
		 * @presetID 预置位ID
		 */
		EVT_EXPORT void  getCurrentTargets(TargetInfoList& outputTargets, int presetID =0);

		/**
		 * 获取当前事件信息
		 * @outputEvents 输出事件信息集合
		 * @evtType 查找类型
		 * @presetID 预置位ID
		 */
		EVT_EXPORT void getCurrentEventInfos(EventInfoList& outputEvents, EventType evtType, int presetID);

		/**
		 * 设置发生新事件回调
		 * @param cb
		 */
		EVT_EXPORT void setNewEvtCallback(EventCallback cb);

		/**
		 * 设置事件撤回回调
		 * @param cb
		 */
		EVT_EXPORT void setWithdrawEvtCallback(EventCallback cb);

		/**
		 * 设置过线目标回调
		 * @param cb
		 */
		EVT_EXPORT void setTargetsPassedCallback(TargetsPassedCallback cb);

        /**
         * 设置车辆过线回调
         * @param cb
         */
        EVT_EXPORT void setVehiclesPassedCallback(TargetsPassedCallback cb);

		/**
		 * 设置车辆信息回调
		 * @param cb
		 */
		EVT_EXPORT void setVehiclesInfoCallback(const VehiclesInfoCallback &cb);

        /**
         * 获取ROI列表
         * @param presetId
         */
        EVT_EXPORT std::vector<Polygon> getRoiList(int presetId);
	private:
		/**
		 * 预置位场景
		 */
		map<int, Scene*> scenes;

		/**
		 * 新事件发生回调函数
		 */
		EventCallback newEvtCallback;
		/**
		 * 事件撤回回调函数
		 */
		EventCallback withdrawEvtCallback;
		/**
		 * 目标过线回调函数
		 */
		TargetsPassedCallback targetsPassedCallback;

        /**
         * 设置车辆过线回调
         */
        TargetsPassedCallback vehiclesPassedCallback;
		/**
		 * 设置车辆信息上报回调
		 */
		VehiclesInfoCallback vehiclesInfoCallback;
		/**
		 * 帧率
		 */
		int frameRate;
		/**
		 * 帧宽
		 */
		int frameWidth;
		/**
		 * 帧高
		 */
		int frameHeight;
		/**
		 * 通道ID
		 */
		int channelID;

		std::mutex sceneMutex;
	};
}
#endif //_EVT_CHANNEL_H