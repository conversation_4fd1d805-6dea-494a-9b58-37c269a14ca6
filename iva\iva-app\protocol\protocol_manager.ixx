/**
 * Project FVM
 */
#include "network.h"
#include "protocol.hpp"
#include "log.h"
#include "module_option.h"

namespace iva::protocol
{
    /**
     * 发送消息至FVM
     * @param msgType 消息类型
     * @param data 消息内容
     * @param processID 进程ID
     */
    template<typename T>
    bool ProtocolManager::sendToFVM(network::ProtocolType msgType, const T &data, int processID)
    {
        auto msg  = network::generateProtocol<T>(data, msgType, network::SessionType::IVA);
        auto returnVal = network::sendDataTo(network::SessionType::FVM, msg, processID);

        if(!returnVal && network::lastErrorCode())
        {
            IVA_LOG_ERROR("FVM msg sent failed! protocol {} process {} code {} err {}",
                         getProtocolName(msgType), processID, network::lastErrorCode(), network::lastErrorMsg());
        }
        else if ( returnVal )
        {
            if (opt::getOptionEnabled(opt::SENDER_MSG_OPT))
                IVA_LOG_INFO("To FVM processID {}, {}", processID, msg);
        }
        return returnVal;
    };

    /**
     * 发送消息至WEB
     * @param msgType 消息类型
     * @param data 消息内容
     * @param isPlatform 是否平台
     */
    template<typename T>
    bool ProtocolManager::sendToWEB(network::ProtocolType msgType, const T &data, bool isPlatform)
    {
        auto msg  = network::serialize<T>(data);
        int targetID = isPlatform ? network::WEB_PLATFORM: network::WEB_LOCAL;
        assert(network::webAPIs.find(msgType) != network::webAPIs.end());

        auto api = network::webAPIs[msgType];
        auto returnVal = network::sendDataTo(network::SessionType::WEB, msg, targetID, api);

        if(!returnVal && network::lastErrorCode())
        {
            IVA_LOG_ERROR("WEB msg sent failed! isPlatform {} process {} code {} err {}",
                          getProtocolName(msgType), isPlatform, network::lastErrorCode(), network::lastErrorMsg());
        }
        else if ( returnVal && msgType != network::ProtocolType::HTTP_HEART_INFO && msgType != network::ProtocolType::HTTP_STREAM_STATUS )
        {
			if (opt::getOptionEnabled(opt::SENDER_MSG_OPT))
			{
				IVA_LOG_INFO("To WEB {}, {}", (isPlatform ? "Plat " : "Local "), msg.length()<= 200 ? msg : msg.substr(0, 200) + "  ...");
			}
        }
        return returnVal;
    };

    /**
     * 发送数据
     * @param msgType 消息类型
     * @param data 消息内容
     */
    template<typename T>
    bool ProtocolManager::sendToWEBViaUDP(network::ProtocolType msgType, const T& data)
    {
        auto msg = network::generateProtocol<T>(data, msgType, network::SessionType::IVA);
        auto returnVal = network::sendDataTo(network::SessionType::WEB, msg, network::WEB_UDP_CLIENT_ID);

        if(!returnVal && network::lastErrorCode())
        {
            IVA_LOG_ERROR("msg sent failed! protocol {} code {} err {}", getProtocolName(msgType), network::lastErrorCode(), network::lastErrorMsg());
        }
        else
        {
            if (opt::getOptionEnabled(opt::SENDER_MSG_OPT))
                IVA_LOG_INFO("To WEB, {}", msg);
        }
        return returnVal;
    }

    /**
     * 发送数据 （默认UDP）
     * @param host 目标地址
     * @param port 目标端口
     * @param msgType 消息类型
     * @param data 消息内容
     */
    template<typename T>
    bool ProtocolManager::sendTo(const std::string& host, unsigned short port, network::ProtocolType msgType, const T& data)
    {
        auto msg  = network::generateProtocol<T>(data, msgType, network::SessionType::IVA);
        auto returnVal = network::sendDataTo(host, port, msg);

        if(!returnVal && network::lastErrorCode())
        {
            IVA_LOG_ERROR("{} , {} msg sent failed! protocol {} code {} err {}",
                          host, port, getProtocolName(msgType), network::lastErrorCode(), network::lastErrorMsg());
        }
        else
        {
            if (opt::getOptionEnabled(opt::SENDER_MSG_OPT))
                IVA_LOG_INFO("To {}, {}, {}",host, port, msg);
        }
        return returnVal;
    }
}
