#include <optional>
#include "channel_manager.h"
#include "config/global_region_config.h"
#include "protocol_manager.h"
#include "protocol_sender.h"
#include "protocol_utility.h"
#include "pipeline_manager.h"
#include "event_analyser.h"
#include "ivameta.h"
#include "ivautils.h"
#ifdef ENABLE_IMAGE_ANALYSER
#include "image_analyser.h"
#endif

namespace iva::protocol
{
    using namespace std;
    using namespace network;
    namespace
    {
        std::map<int, DetectParam> detectParams;                   //preset : param
        std::map<int, std::map<std::string, std::string> > algorithmParams; //planId : key : value
        int processId = 0;                    //!< iva 进程ID
        int deviceId = 0;                     //!< iva GPU设备ID
        int channelOffsetVal = 0;             //!< 检测通道号到pipeline序号转换偏移值
		int systemConfigIndex = 0;
    }

    void startImageAnalyser(const ChannelRestoreConf &channelConfig, DetectParam &detectParam);

    /**
     * @brief 注册接收信号回调
     * @param processID   进程ID
     * @param deviceID    GPU设备ID
     * @param channelSize 支持的通道数量
     */
    void  registerProtocols(int processID, int deviceID, int channelSize)
    {
		processId = processID;
        deviceId = deviceID;
        channelOffsetVal = 1 + (processId -1) * channelSize;
        PROTOCOL_MANAGER.onResetChannel.connect([](ResetChannel msg){
            resetChannel(msg.channelId);
        });
        PROTOCOL_MANAGER.onSystemConfig.connect([](SystemConfig msg){
            updateSystemConfig(msg);
        });
        PROTOCOL_MANAGER.onAlgorithmParam.connect([](AlgorithmParam msg){
            updateAlgorithmParam(msg);
        });
        PROTOCOL_MANAGER.onChannelBasicConf.connect([](VecChannelBasicConf msg){
            updateBaseChannelConfig(msg);
        });
        PROTOCOL_MANAGER.onChannelDetectParam.connect([](ChannelDetectParam msg){
            updateDetectParam(msg);
        });
        PROTOCOL_MANAGER.onChannelRestore.connect([](ChannelRestoreConf msg){
            restoreChannel(msg);
        });
        PROTOCOL_MANAGER.onChannelPause.connect([](ChannelPauseConf msg){
            pauseChannel(msg.channelId, msg.videoId, msg.reason);
        });
        PROTOCOL_MANAGER.onRequestIvaVideo.connect([](RequestIvaVideo msg){
            if (msg.actionId == 2)
                requestSnap(msg.channelId);
            else
                requestIvaVideo(msg.channelId, msg.streamId);

        });
#ifdef ENABLE_TRACK_FEATURE
        ia::setFeatureCallback([=](ia::FeatureOutputData output){
            if (auto featureInfo = (network::FeatureInfo*)output.userData; featureInfo != nullptr)
            {
                featureInfo->feature = std::move(output.feature);
                featureInfo->plateNum = std::move(output.plateNum);
                featureInfo->plateColor = std::move(output.plateColor);
                PROTOCOL_MANAGER.sendToWEB(ProtocolType::HTTP_IVA_FEATURE_INFO, *featureInfo);
                delete featureInfo;
            }
        }, deviceID);
#endif
    }

    /**
     * @brief 恢复通道检测
     */
    void restoreChannel(const ChannelRestoreConf& channelConfig)
    {
        int index = channelConfig.channelId - channelOffsetVal; //!< 多进程通道和pipeline序号转换
		if (index < 0 || index >= pipeline::getTotalChannelSize())
		{
			IVA_LOG_WARN("restored channel:{} index {} out of range", channelConfig.channelId, index);
			return;
		}

        int presetId = channelConfig.presetId;
        if (detectParams.find(presetId) == detectParams.end())  //can't find preset config
        {
            IVA_LOG_ERROR("channel {} preset {} not found", channelConfig.channelId, presetId);
            return;
        }

        //! 设置pipeline裁剪参数
        auto& detectParam = detectParams[presetId];
        int x = (int)detectParam.area.x * channelConfig.width;
        int y = (int)detectParam.area.y * channelConfig.height;
        int w = (int)detectParam.area.width * channelConfig.width;
        int h = (int)detectParam.area.height * channelConfig.height;
        /// TODO Width Height 为0的情况
        bool fullscreen = false;
        if (detectParam.area.x == 0 && detectParam.area.y == 0 && detectParam.area.width == 1 && detectParam.area.height == 1)
        {
            fullscreen = true;
        }
#ifdef NVIDIA	// 其他平台待验证
        pipeline::setVideoCrop(index, x,y,w,h, fullscreen);
#endif
        //! 开启图像检测
        startImageAnalyser(channelConfig, detectParam);

        //! 开启事件检测
        auto channel = evt::getChannel(index);
        if (channel)
            channel->resume(presetId);

        //! 开启pipeline检测
        int videoId = channelConfig.videoId;
        pipeline::setDetectStatus(index, videoId, presetId, IVA_CHANNEL_RESTORED);

        //! 上报状态 (先上报状态0，后续由pipeline上报1)
        protocol::postDetectStatus(index, videoId, 0);
        IVA_LOG_INFO("restored channel:{} videoId:{} presetId:{}", channelConfig.channelId, videoId, presetId);
    }

    /**
     * @brief reset channel
     */
    void resetChannel(int channelId)
    {
        int index = channelId - channelOffsetVal; //!< 多进程通道和pipeline序号转换
        pipeline::resetChannel(index);
    }

    /**
     * @brief 暂停通道检测
     * @param[in] channelId: 通道id
     * @param[in] videoId:   视频id
     * @param[in] reason:    暂停原因 0其他，1手工，2画检测区,3偏移
     */
    void pauseChannel(int channelId, int videoId, int reason)
    {
        int index = channelId - channelOffsetVal; //!< 多进程通道和pipeline序号转换
		if (index < 0 || index >= pipeline::getTotalChannelSize())
		{
			IVA_LOG_WARN("paused channel:{} index {} out of range", channelId, index);
			return;
		}

        //! 暂停pipeline检测
        auto channelState = reason == IVA_CHANNEL_STOP_REASON_SHIFT? IVA_CHANNEL_PAUSED_SHIFT: IVA_CHANNEL_PAUSED_DEFAULT;
        
        pipeline::setDetectStatus(index, videoId, std::nullopt, channelState);

#ifdef ENABLE_IMAGE_ANALYSER
        //! 暂停图像检测
        auto iaDetectChannel = ia::getChannel(index, deviceId);
        if (iaDetectChannel != nullptr)
        {
            if (videoId <= 0)
                iaDetectChannel->reset();
        }
#endif
        if (reason != IVA_CHANNEL_STOP_REASON_SHIFT)
        {
            //! 暂停事件检测
            auto channel = evt::getChannel(index);
            if (channel)
                channel->pause(0, reason == IVA_CHANNEL_STOP_REASON_SHIFT);

            //! 上报状态
            protocol::postDetectStatus(index, videoId, 0);
            IVA_LOG_INFO("paused channel:{} videoId:{}", channelId, videoId);
        }
    }

    /**
     * @brief 更新系统配置
     */
    void updateSystemConfig(const SystemConfig& config)
    {
#ifdef CAMBRICON_MLU370
        if (!isReinitRequesting())
        {
            if (systemConfigIndex++ > 0)
            {
                systemConfigIndex = 0;
                //for (int index = 0; index < pipeline::getTotalChannelSize(); index++)
                //{
                //	pipeline::resetDecoder(index);
                //}
                //pipeline::stop();
                //pipeline::start();
                //return;
                exit(0);
            }
        }
#endif
        PROTOCOL_MANAGER.setSystemIp(config.localIP, config.platIP); //!< 设置IP，初始化web客户端
        pipeline::setOffsetReturnTime((int)config.offsetRetunTime);
        pipeline::setIvaRecordTime(config.ivaPreRecord, config.ivaRecordTime - config.ivaPreRecord);
        IVA_LOG_INFO("system config, local ip:{} plat ip:{} is show id:{}", config.localIP, config.platIP, config.isShowId);
    }

    /**
     * @brief 更新灵敏度参数
     */
    void updateAlgorithmParam(const AlgorithmParam& param)
    {
        algorithmParams.clear();
        for (auto& p : param.vtParam)
        {
            int planId = p.planId;
            std::map<std::string, std::string> cfg;
            for (auto& sub : p.param)
            {
                cfg[sub.key] = sub.value;
            }
            evt::GlobalRegionConfig::getInstance()->initConfig( planId, cfg);
            algorithmParams[planId] = cfg;
        }
    }

    /**
     * @brief 更新通道基本配置参数
     */
    void updateBaseChannelConfig(const VecChannelBasicConf& configs)
    {
        for (auto& config : configs.vtChannelBasicConf)
        {
            bool hasPtz = false;
            for (auto& info : config.videoInfo)
            {
                if (info.hasPtz > 0)
                    hasPtz = true;
            }
#ifdef ENABLE_IMAGE_ANALYSER
            // image analyser
            int index = config.channelId - channelOffsetVal;  //!< 多进程通道和pipeline序号转换
            auto iaDetectChannel = ia::getChannel(index, deviceId);
            if (iaDetectChannel != nullptr)
            {
                iaDetectChannel->setEnable(ia::DetectorType::Offset, hasPtz);
            }
#endif
        }
    }

    /**
     * @brief 更新通道检测参数
     */
    void updateDetectParam(const ChannelDetectParam& param)
    {
        int index = param.channelId - channelOffsetVal;
		if (index < 0 || index >= pipeline::getTotalChannelSize())
		{
			IVA_LOG_WARN("detect param: channel:{} index {} out of range", param.channelId, index);
			return;
		}
        int videoId = param.videoId;
        for (auto& [presetId, detectParam] : param.presets)
        {
            int planId = detectParam.paramPlanId;
            detectParams[presetId] = detectParam;
            evt::ROIInfoList roiInfo = protocol::transRois(detectParam.roi, detectParam.area);
            evt::initChannel(index, roiInfo, EVT_PROCESS_WIDTH, EVT_PROCESS_HEIGHT, presetId, planId);
            IVA_LOG_INFO("detect param channel:{} videoId:{} presetId:{} planId:{}", param.channelId, videoId, presetId, planId);
        }
#ifdef ENABLE_IMAGE_ANALYSER
        //! 重置图像检测
        auto iaDetectChannel = ia::getChannel(index, deviceId);
        if (iaDetectChannel)
            iaDetectChannel->reset();
#endif
    }

    /**
     * @brief 配置图像检测模块相关参数、开启检测
     */
    void startImageAnalyser(const ChannelRestoreConf &channelConfig, DetectParam &detectParam)
    {
#ifdef ENABLE_IMAGE_ANALYSER
        int index = channelConfig.channelId - channelOffsetVal;
        // Set ROI for ImageAnalyser
        auto iaDetectChannel = ia::getChannel(index, deviceId);
        if (iaDetectChannel != nullptr)
        {
            // 设置 抛洒物  是否开启
            bool throwawayEnabled = getDetectEnabled(detectParam, evt::EventType_Obstacle);
            iaDetectChannel->setEnable(ia::DetectorType::Roiobject, throwawayEnabled);

            // 设置 路障  是否开启
            //  ( 依赖路障、抛洒物、施工 是否至少有一项需检测)
            bool roadblockEnabled = getDetectEnabled(detectParam, evt::EventType_RoadBlock);
            bool constructionEnabled = getDetectEnabled(detectParam, evt::EventType_RoadConstruction);
            iaDetectChannel->setEnable(ia::DetectorType::Roadblock, roadblockEnabled || throwawayEnabled || constructionEnabled);

            // 设置 烟火  是否开启
            bool firesmokeEnabled = getDetectEnabled(detectParam, evt::EventType_FireSmoke);
            iaDetectChannel->setEnable(ia::DetectorType::Firesmoke, firesmokeEnabled);
#if defined(NVIDIA) || defined(CAMBRICON_MLU370)
            bool landslideEnabled = getDetectEnabled(detectParam, evt::EventType_Landslide);
            iaDetectChannel->setEnable(ia::DetectorType::Landslide, landslideEnabled);

            bool weatherSnowEnabled = getDetectEnabled(detectParam, evt::EventType_Weather_Snow);
            iaDetectChannel->setEnable(ia::DetectorType::WeatherSnow, weatherSnowEnabled);
            bool weatherRainEnabled = getDetectEnabled(detectParam, evt::EventType_Weather_Rain);
            iaDetectChannel->setEnable(ia::DetectorType::WeatherRain, weatherRainEnabled);
            bool weatherFogEnabled = getDetectEnabled(detectParam, evt::EventType_Weather_Fog);
            iaDetectChannel->setEnable(ia::DetectorType::WeatherFog, weatherFogEnabled);
            if (weatherSnowEnabled || weatherRainEnabled || weatherFogEnabled)
                iaDetectChannel->setEnable(ia::DetectorType::Weather, true);
#endif
            // 设置 检测子区域
            vector<vector<float>> roiPts = getDetectMask(detectParam, evt::EventType_Obstacle);
            iaDetectChannel->setCheckAreaMask(roiPts, ia::DetectorType::Roiobject);

            // 路障施工检测子区域融合，施工依赖于路障
            auto roiPtsRoadBlock = getRoiDetectMask(detectParam, evt::EventType_RoadBlock);
            auto roiPtsRoadConstruction = getRoiDetectMask(detectParam, evt::EventType_RoadConstruction);

            //! 去重areaId相同的区域
            std::vector<std::vector<float>> fusionArea;
            for (auto [areaId, area] : roiPtsRoadBlock)
            {
                fusionArea.insert(fusionArea.begin(), area.begin(), area.end());
                roiPtsRoadConstruction.erase(areaId);
            }
            for (auto [areaId, area] : roiPtsRoadConstruction)
            {
                fusionArea.insert(fusionArea.begin(), area.begin(), area.end());
            }
            iaDetectChannel->setCheckAreaMask(fusionArea, ia::DetectorType::Roadblock);

            // 偏移检测子区域
            vector<vector<float>> laneLinePts = getDetectLaneLinePts(detectParam);
            iaDetectChannel->setCheckAreaMask(laneLinePts, ia::DetectorType::Offset);

            // 设置偏移检测区 已改为车道线绘制比对，可保留供后续模块使用
            //vector<vector<float>> camOffsetPts;
            //if (!detectParam.deviation.empty())
            //{
            //    vector<float> pts;
            //    for (auto& p : detectParam.deviation)
            //    {
            //        pts.push_back(p.x);
            //        pts.push_back(p.y);
            //    }
            //    camOffsetPts.emplace_back(pts);
            //}

           // iaDetectChannel->start();
        }
#endif
    }

    /**
     * @brief 请求结构化视频
     */
    void requestIvaVideo(int channelId, int streamId)
    {
        int index = channelId - channelOffsetVal;
		if (index < 0 || index >= pipeline::getTotalChannelSize())
		{
			IVA_LOG_WARN("request video: channel:{} index {} out of range", channelId, index);
			return;
		}
        pipeline::requestRtmpStream(index);
    }

    /**
     * @brief 请求截图
     */
    void requestSnap(int channelId)
    {
        int index = channelId - channelOffsetVal;
        pipeline::snapImage(index);
    }


}
