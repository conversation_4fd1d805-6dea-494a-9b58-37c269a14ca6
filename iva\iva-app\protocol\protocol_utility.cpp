#include <iostream>
#include <unistd.h>
#include <sys/stat.h>
#include "protocol_utility.h"
#include "log.h"

namespace iva::protocol
{
    using namespace std;
    /**
     * 坐标点 --> 向量
     * @param points 起始点, 结束点
     * @param rtSrc	 检测区
     */
    evt::Vector2D trans2Vector(const std::vector<network::Point>& points,  network::DetectArea rtSrc)
    {
        evt::Vector2D ptNew;
        if (points.size() >= 2)
        {
            evt::Point pt1 = trans2Point(points[0], rtSrc);
            evt::Point pt2 = trans2Point(points[1], rtSrc);
            ptNew.x = pt2.x - pt1.x;
            ptNew.y = pt2.y - pt1.y;
        }
        return ptNew;
    }

    /**
     * 坐标点 归一化
     * @param pt	 输入点
     * @param rtSrc	 检测区
     */
    evt::Point trans2Point(network::Point pt, network::DetectArea rtSrc)
    {
        evt::Point evtPoint(pt.x, pt.y);
        //若区域长宽都为1，则说明没有裁剪，直接返回
        if ((int)(rtSrc.width) == 1 && (int)(rtSrc.height) == 1)
        {
            return evtPoint;
        }
        evtPoint.x = (pt.x - rtSrc.x) / rtSrc.width;
        evtPoint.y = (pt.y - rtSrc.y) / rtSrc.height;
        if (evtPoint.x < 0)
            evtPoint.x = 0;
        else if (evtPoint.x > 1)
            evtPoint.x = 1;
        if (evtPoint.y < 0)
            evtPoint.y = 0;
        else if (evtPoint.y > 1)
            evtPoint.y = 1;
        return evtPoint;
    }

    /**
     * 坐标点 --> 线
     * @param pt	 点集合
     * @param rtSrc	 检测区
     */
    evt::Line trans2Line(const std::vector<network::Point>& points, network::DetectArea rtSrc)
    {
        auto sz = points.size();
        if (sz < 2)
            return evt::Line();
        evt::Point ptBegin = trans2Point(points[0], rtSrc);
        evt::Point ptEnd = trans2Point(points[sz - 1], rtSrc);
        return evt::Line(ptBegin, ptEnd);
    }

    /**
     * 点集合 --> evt::Polygon
     * @param rect
     * @param rtSrc	 检测区
     */
    evt::Polygon trans2Polygon(const std::vector<network::Point>& points, network::DetectArea rtSrc)
    {
        std::vector<evt::Point> pts;
        for (auto& p : points)
            pts.emplace_back(trans2Point(p, rtSrc));
        return evt::Polygon(pts);
    }

    /**
     * CheckAreaItemNode (wtoesocket) --> evt::RegionInfo
     * @param node
     * @param rtSrc	 检测区
     */
    evt::RegionInfo trans2Region(const network::CheckAreaItemNode& node, network::DetectArea rtSrc)
    {
        evt::RegionInfo ri;
        ri.id = node.areaId;
        ri.eventMask = node.eventProperty;
        ri.targetMask = node.labelProperty;
        ri.polygon = trans2Polygon(node.area, rtSrc);
        std::map<string, string> cfg;
        transParam(node.param, cfg);
        ri.config = evt::RegionConfig(cfg, node.paramPlanId);
        return ri;
    }

    /**
    * vector<Param> --> map<string, string>
    * @param params
    * @param mapValue
    */
    void transParam(const std::vector<network::Param>& params, std::map<std::string, std::string>& mapValue)
    {
        mapValue.clear();
        if (params.empty())
            return;

        for (auto& p : params)
        {
            mapValue[p.key] = p.value;
        }
    }

    /**
     * 车道线信息转换
     * @param lane
     * @param rtSrc
     */
    evt::LaneInfo trans2Lane(const network::Lane& lane, network::DetectArea rtSrc)
    {
        evt::LaneInfo l;
        l.laneType = (evt::LaneType)lane.laneType;
        l.id = lane.laneId;
        l.direction = trans2Vector(lane.direction, rtSrc);
        l.eventMask = lane.eventProperty;
        l.targetMask = lane.labelProperty;
        l.polygon = trans2Polygon(lane.area, rtSrc);
        std::map<string, string> cfg;
        transParam(lane.param, cfg);
        l.config = evt::RegionConfig(cfg, lane.paramPlanId);
        return l;
    }

    /**
    * ROI信息转换
    * @param rois
    * @param rtSrc
    */
    evt::ROIInfoList transRois(const std::vector<network::ROINode>& rois, network::DetectArea rtSrc)
    {
        evt::ROIInfoList roiInfo;
        for (auto& p : rois)
        {
            evt::ROIInfo roi;
            roi.id = p.roiId;
            roi.eventMask = p.eventProperty;
            roi.targetMask = p.labelProperty;
            roi.polygon = trans2Polygon(p.area, rtSrc);
            std::map<string, string> cfg;
            transParam(p.param, cfg);
            roi.config = evt::RegionConfig(cfg, p.paramPlanId);
            roi.logicID = p.logicId;
            roi.direction = trans2Vector(p.direction, rtSrc);
            roi.countLine = trans2Line(p.countLine, rtSrc);
            roi.referenceLine = trans2Line(p.keyPoint, rtSrc);
            roi.referenceRect = trans2Polygon(p.keyRect, rtSrc);
            roi.referenceDistance = p.keyLength;
            for (auto& c : p.checkArea)
                roi.childRegions.emplace_back(trans2Region(c, rtSrc));
            for (auto& c : p.lane)
                roi.laneRegions.emplace_back(trans2Lane(c, rtSrc));

            roi.featureRoiInfo.polygon = trans2Polygon(p.featureArea.area, rtSrc);
            roi.featureRoiInfo.beginTime = p.featureArea.beginTime;
            roi.featureRoiInfo.endTime = p.featureArea.endTime;

           if (!roi.featureRoiInfo.beginTime.empty() && !roi.featureRoiInfo.endTime.empty())
           {
               try {
                   sscanf(roi.featureRoiInfo.beginTime.c_str(), "%d:%d:%d", &roi.featureRoiInfo.beginHour, &roi.featureRoiInfo.beginMinute, &roi.featureRoiInfo.beginSecond);
                   sscanf(roi.featureRoiInfo.endTime.c_str(), "%d:%d:%d", &roi.featureRoiInfo.endHour, &roi.featureRoiInfo.endMinute, &roi.featureRoiInfo.endSecond);
               }
               catch (const std::exception& e){
                   IVA_LOG_ERROR("Failed to convert beginTime{} endTime{} to int, error {}", roi.featureRoiInfo.beginTime, roi.featureRoiInfo.endTime, e.what())
               }
           }

            roiInfo.emplace_back(roi);
        }
        return roiInfo;
    }

    /**
    * 基于事件类型 获取检测配置的子区域（坐标点集合）
    * @param detectParam 检测参数
    * @param eventType 事件类型
    */
    std::vector<std::vector<float>> getDetectMask(const network::DetectParam& detectParam, evt::EventType eventType)
    {
        vector<vector<float>> roiPts;
        for (auto& roiNode : detectParam.roi)
        {
            for (auto& checkArea : roiNode.checkArea)
            {
                int evtProperty = checkArea.eventProperty;

                uint_fast32_t mask = 1;
                int move = static_cast<int>(eventType) - 1;
                mask = mask << move;
                bool bEnable = (evtProperty & mask) >> move;

                if (bEnable)
                {
                    vector<float> pts;
                    for (auto& p : checkArea.area)
                    {
                        pts.push_back(p.x);
                        pts.push_back(p.y);
                    }
                    roiPts.emplace_back(pts);
                }
            }
        }
        return roiPts;
    }

    /**
     * 基于事件类型 获取子区域的区域配置（坐标点集合）
     * @param detectParam 检测参数
     * @param eventType 事件类型
     * @return 子区域Id对应的坐标点集合 key areaId, value:子区域坐标点集合
     */
    std::map<int, std::vector<std::vector<float>>> getRoiDetectMask(const network::DetectParam& detectParam, evt::EventType eventType)
    {
        std::map<int, std::vector<std::vector<float>>> roiPoints;
        for (auto& roiNode : detectParam.roi)
        {
            for (auto& checkArea : roiNode.checkArea)
            {
                int evtProperty = checkArea.eventProperty;

                uint_fast32_t mask = 1;
                int move = static_cast<int>(eventType) - 1;
                mask = mask << move;
                bool bEnable = (evtProperty & mask) >> move;

                if (bEnable)
                {
                    vector<float> pts;
                    for (auto& p : checkArea.area)
                    {
                        pts.push_back(p.x);
                        pts.push_back(p.y);
                    }
                    roiPoints[checkArea.areaId].emplace_back(pts);
                }
            }

            for (auto& lane : roiNode.lane)
            {
                bool bEnable = isEventPropertyEnabled(lane.eventProperty, eventType);
                if (bEnable)
                {
                    vector<float> pts;
                    for (auto& p : lane.area)
                    {
                        pts.push_back(p.x);
                        pts.push_back(p.y);
                    }
                    roiPoints[lane.laneId].emplace_back(pts);
                }
            }
        }
        return roiPoints;
    }

    /**
    * 获取事件掩码属性是否开启
    * @param evtProperty 事件掩码
    * @param eventType 事件类型
    */
    bool isEventPropertyEnabled(int evtProperty, evt::EventType eventType)
    {
        uint_fast32_t mask = 1;
        int move = static_cast<int>(eventType) - 1;
        mask = mask << move;
        bool bEnable = (evtProperty & mask) >> move;
        return bEnable;
    }

    /**
    * 获取检测状态是否需要开启
    * @param detectParam 检测参数
    * @param eventType 事件类型
    */
    bool getDetectEnabled(const network::DetectParam& detectParam, evt::EventType eventType)
    {
        for (auto& roiNode : detectParam.roi)
        {
            if (isEventPropertyEnabled(roiNode.eventProperty, eventType))
                return true;
            // 车道中新增了施工事件
            for (auto& lane : roiNode.lane)
            {
                if (isEventPropertyEnabled(lane.eventProperty, eventType))
                    return true;
            }
            for (auto& checkArea : roiNode.checkArea)
            {
                if (isEventPropertyEnabled(checkArea.eventProperty, eventType))
                    return true;
            }
        }
        return false;
    }

    /**
     * 创建路径
     */
    bool createPath(const std::string& szPath)
    {
        int ret = access(szPath.c_str(), 0);
        if ( ret == 0 )
            return true;
        auto pos = szPath.find('/',1 );
        while (pos != std::string::npos)
        {
            std::string szSub = szPath.substr(0, pos);
            if (access(szSub.c_str(), 0) != 0)
            {
                if (mkdir(szSub.c_str(), 0755) == -1)
                    return false;
            }
            pos = szPath.find('/', pos + 1);
            if (pos == std::string::npos)
                break;
        }
        return true;
    }

    /**
     * 获取检测参数中的车道线，vector point 转成 vector float
     * @param detectParam
     */
    std::vector<std::vector<float>> getDetectLaneLinePts(const network::DetectParam &detectParam)
    {
        vector<vector<float>> laneLinePts;
        for (const auto& laneLine : detectParam.laneLine)
        {
            vector<float> line;
            for (const auto& point : laneLine)
            {
                line.emplace_back(point.x);
                line.emplace_back(point.y);
            }
            laneLinePts.emplace_back(move(line));
        }
        return laneLinePts;
    }

}