/**
 * Project AI事件分析模块
 */


#ifndef _ROI_H
#define _ROI_H


#include "area.h"
#include "lane.h"
#include "feature_roi.h"
#include "area/roi_info.h"
#include "opencv2/core.hpp"

/**
* 感兴趣区 (ROI)
*/
namespace evt
{
	//  m/s -> km/h 转换率
	#define MS_2_KMH  3.6

	class ROI : public Area {
	public:

		ROI();
		~ROI();
		/**
		 * 初始化ROI相关(检测器， 配置等)
		 *
		 * @param info  ROI信息
		 */
		ROI(ROIInfo info);

		/**
		 * 清除目标
		 */
		void clearTargets() override;

		/**
		 * 匹配区域
		 */
		Area* matchArea(TargetPtr target);


		/**
		 * 清除事件物体
		 */
		void clearEventObjects() override;

		/**
		 * 匹配事件物体
		 */
		bool matchEventObjects(EventObject& object);

		/**
		 * 更新目标计数线位置状态
		 */
		void updateCountLineState(TargetPtr target);

		/**
		 * 更新目标速度
		 * @param target
		 */
		int updateSpeed(TargetPtr target);

		/**
		 * 设置事件地图
		 * @param map
		 */
		void setEventMap(EventMap* map) override;

		/**
		 * 设置目标地图
		 * @param map
		 */
		void setTargetMap(TargetMap* map) override;

		/**
		 * 设置检测尺寸
		 */
		void setFrameSize(int w, int h) override;

		/**
		 * 设置开始检测时间
		 */
		void setStartDetectTime(steady_clock::time_point time) override;

		/**
		 * 设置检测帧率
		 */
		void setFrameRate(int rate);

		/**
		 * 检测事件
		 */
		void detectEvents();

		/**
		 * 获取逻辑ID
		 */
		int getLogicID();

		/**
		 * 获取距离映射比率
		 */
		double getDistanceScale();

		/**
		 * 获取转换矩阵
		 */
		cv::Mat getTransformMatrix();

		/**
		 * 设置拥堵状态
		 */
		void setJamState(bool injam);

		/**
		 * 设置施工状态
		 */
		void setConstructionState(bool inConstruction) override;

		/**
		 * 设置偏移状态
		 */
		void setOffsetState(bool offset);

		/**
		 * 设置场景状态
		 */
		void setSceneState(SceneState* state);

        /**
         * 是否在特征检测区域内
         */
        inline bool isEnableRetrieveFeature(Rect rect){return (featureRoi && featureRoi->isEnableRetrieveFeature(rect));}

        /**
         * 获取特征检测区底部边界值
         */
        inline std::optional<float> getFeatureRoiBottom(){return (featureRoi && featureRoi->getBottom() > 0) ? std::make_optional(featureRoi->getBottom()) : std::nullopt;};

	private:


		/**
		 * 初始化检测器
		 */
		void initDetectors(std::vector<EventType>& globalTypes) override;

		/**
		 * 初始化转换矩阵
		 *
		 * @param referenceRect  参考矩阵
		 * @param referenceLine  参考线
		 * @param referenceDistance  参考距离
		 */
		void initTransformMatrix(Polygon& referenceRect, Line& referenceLine, int referenceDistance);

		/**
		 * ROI 逻辑ID
		 */
		int logicID;

		/**
		 * (子)区域列表
		 */
		std::vector<Region*> regionList;

		/**
		 * 车道列表
		 */
		std::vector<Lane*> laneList;

        /**
         * 特征提取区域信息
         */
        std::shared_ptr<FeatureRoi> featureRoi = nullptr;

		/**
		 * 计数线
		 */
		Line countLine;

		/**
		 * 距离映射比率
		 */
		double distanceScale;

		/**
		 * 检测帧率
		 */
		int frameRate;

		/**
		 * 转换矩阵
		 */
		cv::Mat transformMatrix;
	};
}
#endif //_ROI_H