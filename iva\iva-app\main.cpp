#include <iostream>
#include <gst/gst.h>
#ifdef NVIDIA
#include <cuda_runtime_api.h>
#endif

#include "module_option.h"
#include "ivaconfig.hpp"
#include "log.h"
#include "src/channel_manager.h"
#include "src/pipeline_manager.h"
#include "protocol_manager.h"
#include "protocol_sender.h"
#include "malloc.h"
#include "device_util.h"
#include <sys/wait.h>

using namespace std;
using namespace iva;
void onSignalAction(int signum, siginfo_t* siginfo, void* ucontext);
void process(int totalChannelSize, int processId, int deviceId);
bool checkDevice(int totalChannelSize, int processId, int deviceId);

static pid_t childPid = 0;
int main(int argc, char *argv[])
{
	#ifdef CAMBRICON_MLU370
	    mallopt(M_MMAP_MAX, 0);         ///< 禁止使用mmap进行大块内存分配，避免内存碎片
	    mallopt(M_TRIM_THRESHOLD, 0);   ///< 禁止使用madvise进行内存释放，避免内存碎片
	#endif

    std::cout << "IVA Version: " << getVersion() << std::endl;

	struct sigaction signalAction = {};
	signalAction.sa_sigaction = onSignalAction;
	signalAction.sa_flags = SA_SIGINFO;
	sigaction(SIGINT, &signalAction, nullptr);
	sigaction(SIGTERM, &signalAction, nullptr);
	sigaction(SIGUSR1, &signalAction, nullptr);
	sigaction(SIGUSR2, &signalAction, nullptr);

	int totalChannelSize = 16;
	int processId = 1;
	int deviceId = 0;

    try{
        totalChannelSize = (argc > 1) ? std::stoi(argv[1]) : totalChannelSize;
		processId = (argc > 2) ? std::stoi(argv[2]) : processId;
        deviceId = (argc > 3) ? std::stoi(argv[3]) : deviceId;
    }
    catch (std::exception& e){
        std::cerr << "\n\n\n运行参数配置错误:" << e.what() << "\n\n\n";
        return 0;
    }

	pid_t pid = fork();
	if (pid == -1) {
		perror("fork");// 错误处理
        if (!checkDevice(totalChannelSize, processId, deviceId))
            return 1;

		process(totalChannelSize, processId, deviceId);
		return 1;
	}
	else if (pid == 0) {
        if (!checkDevice(totalChannelSize, processId, deviceId))
            return 1;
		process(totalChannelSize, processId, deviceId);
		return 0;
	}
	else {
		std::cout << "Parent process, PID: " << getpid() << ", Child PID: " << pid << std::endl;
		int status;
		while (true) {
			childPid = wait(&status);
			if (childPid == pid) {
				std::cout << "Child process with PID " << childPid << " exited." << std::endl;
				pid = fork();
				if (pid == -1) {
					perror("fork");
					return 1;
				}
				else if (pid == 0) {
					process(totalChannelSize, processId, deviceId);
					return 0;
				}
				else {
					std::cout << "Parent process, PID: " << getpid() << ", New Child PID: " << pid << std::endl;
				}
			}
		}
	}

	return 0;
}

void process(int totalChannelSize, int processId, int deviceId)
{
	std::string logPath = std::string(LOG_NAME) + "_" + to_string(processId);
	SETUP_LOG(LOG_NAME, logPath);
	setLogEnabled(SHOW_IVA_FRAME, true);

	protocol::registerProtocols(processId, deviceId, totalChannelSize);
	pipeline::init(totalChannelSize, processId, deviceId, [=]() {
		protocol::PROTOCOL_MANAGER.init(processId);  //!< 进程号为GPU设备号+1
		protocol::initStatusManager();                  //!< 启动与web服务断的心跳包线程
		iva::protocol::sendRequestInit(processId, totalChannelSize);
		});

#ifndef NVIDIA
	devInit();
#endif // !NVIDIA

	pipeline::start();
	std::string input;
	while (pipeline::isRunning())
	{
		std::cin >> input;
		if (input == "quit")
		{
			pipeline::stop();
			break;
		}
		else if (input == "status")
		{
			pipeline::printStatus();
		}
		else
		{
			char* ptr;
			strtol(input.c_str(), &ptr, 10);
			if (*ptr == '\0')
			{
				try {
					int i = std::stoi(input);
					pipeline::printLog(i);
				}
				catch (...)
				{
				}
			}
			else if (input == "track")
			{
				switchLogStatus(SHOW_IVA_TRACK);
			}
			else if (input == "fps")
			{
				switchLogStatus(SHOW_IVA_FPS);
			}
			else if (input == "frame")
			{
				switchLogStatus(SHOW_IVA_FRAME);
			}
			else if (input == "v")
			{
				std::cout << "IVA : " << getVersion() << std::endl;
			}
			else if (input == "trace")
			{
				gst_debug_set_default_threshold(GST_LEVEL_TRACE);
			}
			else if (input == "log")
			{
				gst_debug_set_default_threshold(GST_LEVEL_LOG);
			}
			else if (input == "debug")
			{
				gst_debug_set_default_threshold(GST_LEVEL_DEBUG);
			}
			else if (input == "info")
			{
				gst_debug_set_default_threshold(GST_LEVEL_INFO);
			}
			else if (input == "warn")
			{
				gst_debug_set_default_threshold(GST_LEVEL_WARNING);
			}
			else if (input == "error")
			{
				gst_debug_set_default_threshold(GST_LEVEL_ERROR);
			}
			else if (input.find("reset") != std::string::npos)
			{
				size_t last_index = input.find_last_not_of("0123456789");
				string stream_id_str = input.substr(last_index + 1);
				if (!stream_id_str.empty())
				{
					int channel = std::stoi(stream_id_str);
					pipeline::resetDecoder(channel);
				}
			}
			else
			{
				opt::parseOption(input);
			}
		}
	}
	protocol::PROTOCOL_MANAGER.dispose();
	pipeline::stop();
	protocol::disposeStatusManager();

#ifndef NVIDIA
	devFinalize();
#endif // !NVIDIA
	IVA_LOG_DEBUG("[IVA]==process exit.");
	FINISH_LOG();
}

void onSignalAction(int signum, siginfo_t* siginfo, void* ucontext)
{
	switch (signum) {
	case SIGINT:
	case SIGTERM:
	case SIGUSR1:
	case SIGUSR2:
		if (childPid != 0) {
			kill(childPid, SIGTERM);
		}
		exit(0);
		break;
	default:
		break;
	}
}

bool checkDevice(int totalChannelSize, int processId, int deviceId)
{
    auto deviceInfo = getDeviceInfo(deviceId);
    if (deviceInfo.status)
    {
        fprintf(stderr, "Device: %s, id: %d, error: %d,  please check the driver, device status and runtime etc.\n", deviceInfo.name.c_str(), deviceId, deviceInfo.status);
        std::cerr << "Press any key to exit!" << std::endl;
        std::getchar();
        return false;
    }
    IVA_LOG_INFO("[process]:{}, [device]: {}, [id]: {}, [channel size]: {}", processId, deviceInfo.name, deviceId, totalChannelSize);
    return true;
}