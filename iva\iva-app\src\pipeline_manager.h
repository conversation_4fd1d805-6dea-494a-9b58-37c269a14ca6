/**
 * Project IVA
 */
#pragma once
#include <optional>
#include "ivameta.h"

namespace iva::pipeline
{
	/**
	 * @brief 初始化gstreamer, 注册pipeline始化完成回调
	 * @param channelSize 支持的通道数量
	 * @param processID   进程ID
	 * @param deviceID    gpu设备ID
	 * @param callback    pipeline初始化完成外部回调函数
	 */
	void init(int channelSize, int processID, int deviceID, const std::function<void()>& callback);

    /**
     * @brief 启动pipeline
     */
    void start();

    /**
     * @brief 停止pipeline
     */
    void stop();

    /**
     * @brief 获取pipeline运行状态
     */
    bool isRunning();

    /**
     * @brief 所有通道的pipeline创建、连接、启动
     */
    int process();

    /**
     * @brief 配置通道日志是否开启输出
     */
    void printLog(int index, bool enable = true);

    /**
     * @brief 打印通道状态
     */
    void printStatus();

    /**
     * @brief 重置裁剪参数
     * @param index 插件序号，对应通道号-1
     * @param x,y,w,h 裁剪参数
     * @param fullscreen 是否全屏
     */
    void setVideoCrop(int index, int x, int y, int w, int h, bool fullscreen);

    /**
     * @brief 重置解码模块
     * @param index 插件序号，对应通道号-1
     */
    void resetDecoder(int index);

    /**
     * @brief 设置通道的检测状态
     * @param index 插件序号，对应通道号-1
     */
    void setDetectStatus(int index, int videoId, std::optional<int> presetId, IVAChannelState detect);

    /**
     * @brief 设置所有通道的偏移归位时间
     * @param offsetReturnTime 偏移归位时间
     */
    void setOffsetReturnTime(int offsetReturnTime);

    /**
     * @brief reset channel
     */
    void resetChannel(int index);

    /**
     * @brief 请求推rtmp流
     */
    void requestRtmpStream(int index);

    /**
     * @brief 设置视频录制时间配置参数
     * @param[in] beforeTime 相对于当前时间的预录像时间
     * @param[in] afterTime  相对于当前时间的持续录像时间
     */
    void setIvaRecordTime(uint beforeTime, uint afterTime);

	/**
	* @brief 获取总通道数量
	*/
	int getTotalChannelSize();

    /**
     * @brief 截图
     */
    void snapImage(int index);
}
