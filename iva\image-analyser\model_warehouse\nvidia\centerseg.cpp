#include "centerseg.h"

#include <utility>

namespace model_warehouse
{
    /**
     * 模型加载，资源初始化
     */
    void Centerseg::initResource()
    {
        if (!modelInited)
        {
            if (!predictor.init(std::move(modelPath), deviceId))
                return;

            initInputDimensions();
            initOutputDimensions();

            if (!scoreDataHost)
                scoreDataHost = std::shared_ptr<float []>(new float[batchSize * outputChannel * outputWidth * outputHeight]);
            if (!scoreDataDevice)
                CHECK_CUDA_STATUS(cudaMalloc(&scoreDataDevice, batchSize * outputChannel * outputWidth * outputHeight * sizeof(float)), "cudaMalloc failed", true);
            if (!imageDataDevice)
                CHECK_CUDA_STATUS(cudaMalloc(&imageDataDevice, batchSize * inputWidth * inputHeight * inputChannel * sizeof(float)), "cudaMalloc failed", true);

            modelInited = true;
        }
    }

    /**
     * @brief                  图像预处理，padding、颜色通道转换、尺寸缩放
     * @param[in] images       原始输入图像
     * @param[in] padding      是否进行padding
     * @param[in] convertRGB   是否进行图像颜色转换
     * @param[out] imageData   预处理后输出的图像数据
     */
    bool Centerseg::preprocess(std::vector<cv::Mat>& images, bool padding, bool convertRGB, vector<float>& imageData)
    {
        std::vector<cv::Mat> tempImages = padding ? imagePadding(images) : images;

        for (auto& image : tempImages)
            imagePreprocess(image, imageData);

        return true;
    }

    /**
     * @brief                  执行一次图像推理流程：预处理、推理、后处理输出
     * @param[in] images       原始输入图像
     * @param[in]  padding     是否进行图像padding
     * @return                 推理后处理输出结果
     */
    cv::Mat Centerseg::infer(std::vector<cv::Mat> images, bool padding)
    {
        vector<float> imageData;
        preprocess(images, false, false, imageData);

        CHECK_CUDA_STATUS(cudaMemcpy(imageDataDevice, imageData.data(), imageData.size() * sizeof(float), cudaMemcpyHostToDevice), "Failed to copy image data to device" , false);
        vector<void *> buffers = {imageDataDevice , scoreDataDevice };
        if (!predictor.infer(buffers, batchSize))
            return {};

        return postprocess((int)images.size());
    }

    /**
     * 模型推理 （预处理，推理，后处理）
     * @param[in] img         输入帧
     * @param[in] scoreThres  阈值
     * @param[in] padding     是否进行padding预处理
     * @return
     */
    cv::Mat Centerseg::postprocess(int imageSize)
    {
        CHECK_CUDA_STATUS(cudaMemcpy(scoreDataHost.get(), scoreDataDevice, batchSize * outputChannel * outputWidth * outputHeight * sizeof(float), cudaMemcpyDeviceToHost), "Failed to copy score data to host", false);
        cv::Mat binary = cv::Mat::zeros(cv::Size(outputWidth, outputHeight), CV_8UC1);
        for (int i = 0; i < outputChannel; i++)
        {
            for (int j = 0; j < outputHeight; j++)
            {
                for (int k = 0; k < outputWidth; k++)
                {
                    if (scoreDataHost[i * outputWidth * outputHeight + j * outputWidth + k] > scoreThreshold)
                        binary.at<uchar>(j, k) = 255;
                }
            }
        }
        return binary;
    }
}
