#include "pipeline_factory.h"
#include "ivaconfig.hpp"
#include "ivameta.h"

namespace iva::pipeline
{
	/**
	 * @brief 创建单个通道的pipeline的sink bin(裁剪、iva、编码、推流 等插件配置)
	 * @param[in] 通道序号(从0开始)
	 */
	GstElement* createSinkBin(guint index)
	{
		gchar elem_name[50];

		// sink bin
		GST_ELEMENT_INDEX_NAME(elem_name, "sink_bin", index);
		auto sink_bin = gst_bin_new(elem_name);
		GST_ELEMENT_CHECK(sink_bin, elem_name);
		ElementBin elementBin(sink_bin);

#ifdef NVIDIA
		// nvvidconv 1
		GST_ELEMENT_INDEX_NAME(elem_name, "sink_conv_first_", index);
		auto sink_conv_first = gst_element_factory_make(AI_CONVERTER, elem_name);
		GST_ELEMENT_CHECK(sink_conv_first, elem_name);
		g_object_set(G_OBJECT(sink_conv_first), "gpu-id", deviceId, NULL);
		elementBin.linkNext(sink_conv_first);
#endif

#ifndef NVIDIA
 		// TODO 寒武纪tracker 暂不支持批量及时序跟踪 暂放至sink部分
 		// core queue2
 		GST_ELEMENT_INDEX_NAME(elem_name, "core_queue2", index);
 		auto core_queue2 = gst_element_factory_make("queue", elem_name);
        g_object_set(G_OBJECT(core_queue2), "leaky", 2, NULL);
        g_object_set(G_OBJECT(core_queue2), "flush-on-eos", true, NULL);
 		GST_ELEMENT_CHECK(core_queue2, elem_name);
 		elementBin.linkNext(core_queue2);

 		// tracker
 		GST_ELEMENT_INDEX_NAME(elem_name, "core_tracker", index);
 		auto core_tracker = gst_element_factory_make(AI_TRACKER, elem_name);
 		GST_ELEMENT_CHECK(core_tracker, elem_name);
#ifdef HUAWEI
		g_object_set(G_OBJECT(core_tracker), "kalman-filter", TRUE, NULL);
		g_object_set(G_OBJECT(core_tracker), "hungarian-matching", TRUE, NULL);
		char class_coast_map[64] = {};
		sprintf(class_coast_map, "%d>50:%d>50:%d>0", IVA_TARGET_TYPE_FIRE, IVA_TARGET_TYPE_SMOKE, IVA_TARGET_TYPE_ROADBLOCK);
		g_object_set(G_OBJECT(core_tracker),  "class-coast-map", class_coast_map, NULL);
#endif

#ifdef CAMBRICON
		char class_coast_map[64] = {};
		sprintf(class_coast_map, "%d>25:%d>25:%d>25", IVA_TARGET_TYPE_FIRE, IVA_TARGET_TYPE_SMOKE, IVA_TARGET_TYPE_LIGHT);
		g_object_set(G_OBJECT(core_tracker),  "class-coast-map", class_coast_map, NULL);
#endif
 		elementBin.linkNext(core_tracker);
#endif

		// image analyser queue
		GST_ELEMENT_INDEX_NAME(elem_name, "image_analyser_queue", index);
		auto image_analyser_queue = gst_element_factory_make("queue", elem_name);
		GST_ELEMENT_CHECK(image_analyser_queue, elem_name);
		elementBin.linkNext(image_analyser_queue);

		// image analyser
		GST_ELEMENT_INDEX_NAME(elem_name, "sink_image_analyser_", index);
		auto sink_image_analyser = gst_element_factory_make("imageanalyser", elem_name);
		GST_ELEMENT_CHECK(sink_image_analyser, elem_name);
		g_object_set(G_OBJECT(sink_image_analyser), "gpu-id", deviceId, NULL);
		elementBin.linkNext(sink_image_analyser);

		// sink queue
		GST_ELEMENT_INDEX_NAME(elem_name, "sink_queue", index);
		auto sink_queue = gst_element_factory_make("queue", elem_name);
		GST_ELEMENT_CHECK(sink_queue, elem_name);
		elementBin.linkNext(sink_queue);

		// ivaplugin1
		GST_ELEMENT_INDEX_NAME(elem_name, "sink_ivaplugin1_", index);
		auto sink_ivaplugin1 = gst_element_factory_make("ivaplugin1", elem_name);
		GST_ELEMENT_CHECK(sink_ivaplugin1, elem_name);
		g_object_set(G_OBJECT(sink_ivaplugin1), "gpu-id", deviceId, NULL);
		elementBin.linkNext(sink_ivaplugin1);

		// rtmp bin
		auto rtmp_bin = createRtmpSink(index);
		GST_ELEMENT_CHECK(rtmp_bin, "rtmp bin");
		gst_bin_add_many(GST_BIN(sink_bin), rtmp_bin, NULL);
		//elementBin.linkNext(rtmp_bin);

		// fakesink
		//GST_ELEMENT_INDEX_NAME(elem_name, "sink_fakesink", index);
		//auto sink_fakesink = gst_element_factory_make("fakesink", elem_name);
		//GST_ELEMENT_CHECK(sink_fakesink, elem_name);
		//g_object_set(G_OBJECT(sink_fakesink), "sync", FALSE, "async", FALSE, "enable-last-sample", FALSE, NULL);
		//elementBin.linkNext(sink_fakesink);

		// create ghost sink pad for bin
		NVGSTDS_BIN_ADD_GHOST_PAD(sink_bin, elementBin.first, "sink");
		return sink_bin;
	}

	/**
	* @brief 销毁rtmp sink相关元件
	*/
	void destroyRtmpSink(int channel)
	{
		gchar elem_name[50];

		g_snprintf(elem_name, sizeof(elem_name), "sink_bin%d", channel);
		GstElement* sink_bin = gst_bin_get_by_name(GST_BIN(mainPipeline), elem_name);
		GST_ELEMENT_CHECK2(sink_bin, elem_name);

		// encode bin
		GST_ELEMENT_INDEX_NAME(elem_name, "encode_bin", channel);
		auto encode_bin = gst_bin_get_by_name(GST_BIN(sink_bin), elem_name);
		GST_ELEMENT_CHECK2(encode_bin, elem_name);

		gst_element_set_state(encode_bin, GST_STATE_NULL);
		gst_bin_remove(GST_BIN(sink_bin), encode_bin);
	}

	/**
	 * @brief 创建rtmp sink相关元件
	 */
	GstElement* createRtmpSink(int channel)
	{
		gchar elem_name[50];

		// encode bin
		GST_ELEMENT_INDEX_NAME(elem_name, "encode_bin", channel);
		auto encode_bin = gst_bin_new(elem_name);
		GST_ELEMENT_CHECK(encode_bin, elem_name);
		ElementBin elementBin(encode_bin);

		// nvvidconv 2
		GST_ELEMENT_INDEX_NAME(elem_name, "sink_conv_second", channel);
		auto sink_conv_second = gst_element_factory_make(AI_CONVERTER, elem_name);
		GST_ELEMENT_CHECK(sink_conv_second, elem_name);
#ifdef NVIDIA
		g_object_set(G_OBJECT(sink_conv_second), "gpu-id", deviceId, NULL);
#else
		g_object_set(G_OBJECT(sink_conv_second), "npu-id", deviceId, NULL);
#endif
		elementBin.linkNext(sink_conv_second);

        gchar caps_str[128];
#ifndef NVIDIA
		// capsfilter
		GST_ELEMENT_INDEX_NAME(elem_name, "crop_capsfilter", channel);
		auto conv_capsfilter = gst_element_factory_make("capsfilter", elem_name);
		GST_ELEMENT_CHECK(conv_capsfilter, elem_name);


		#ifdef HUAWEI
		g_snprintf(caps_str, sizeof(caps_str), "video/x-raw, width=%d, height=%d", SETTINGS->videoWidth(), SETTINGS->videoHeight());
        #elif defined(CAMBRICON_MLU370)
        g_snprintf(caps_str, sizeof(caps_str), "video/x-raw, width=%d, height=%d, format=RGB", SETTINGS->videoWidth(), SETTINGS->videoHeight());
        #elif defined(CAMBRICON_MLU270)
        g_snprintf(caps_str, sizeof(caps_str), "video/x-raw, width=%d, height=%d, format=RGB", 640, 640); ///< TODO 格式、尺寸从推理插件获取
        #elif defined(CAMBRICON_MLU220)
        g_snprintf(caps_str, sizeof(caps_str), "video/x-raw, width=%d, height=%d, format=RGBA", SETTINGS->videoWidth(), SETTINGS->videoHeight());
		#endif
		GstCaps* conv_caps = gst_caps_from_string(caps_str);
		g_object_set(G_OBJECT(conv_capsfilter), "caps", conv_caps, NULL);
		elementBin.linkNext(conv_capsfilter);
#endif
		int framerate = SETTINGS->framerate();
		if (framerate > 0)
		{
			// video rate
			GST_ELEMENT_INDEX_NAME(elem_name, "video_rate", channel);
			auto video_rate = gst_element_factory_make("videorate", elem_name);
			GST_ELEMENT_CHECK(video_rate, elem_name);
			elementBin.linkNext(video_rate);

			// fps capsfilter
			GST_ELEMENT_INDEX_NAME(elem_name, "fps_capsfilter", channel);
			auto fps_capsfilter = gst_element_factory_make("capsfilter", elem_name);
			GST_ELEMENT_CHECK(fps_capsfilter, elem_name);

			gchar fps_str[128];
			g_snprintf(fps_str, sizeof(caps_str), "video/x-raw,framerate=%d/1", SETTINGS->framerate());
			auto fps_caps = gst_caps_from_string(fps_str);
			g_object_set(G_OBJECT(fps_capsfilter), "caps", fps_caps, NULL);
			elementBin.linkNext(fps_capsfilter);
		}

		//stable queue
		GST_ELEMENT_INDEX_NAME(elem_name, "stable_queue", channel);
		GstElement* stable_queue = gst_element_factory_make("stablequeue", elem_name);
		GST_ELEMENT_CHECK(stable_queue, elem_name);
		elementBin.linkNext(stable_queue);

		// sink encode queue
		GST_ELEMENT_INDEX_NAME(elem_name, "sink_encode_queue", channel);
		auto sink_encode_queue = gst_element_factory_make("queue", elem_name);
		GST_ELEMENT_CHECK(sink_encode_queue, elem_name);
#ifndef NVIDIA
		g_object_set(G_OBJECT(sink_encode_queue), "leaky", 2, NULL);
		g_object_set(G_OBJECT(sink_encode_queue), "flush-on-eos", true, NULL);
#endif
		elementBin.linkNext(sink_encode_queue);

#ifdef CAMBRICON
        // video converter
        g_snprintf(elem_name, sizeof(elem_name), "videoconvert%d", 0);
        auto videoconvert = gst_element_factory_make("videoconvert", elem_name);

        GST_ELEMENT_CHECK(videoconvert, elem_name);
        elementBin.linkNext(videoconvert);

        // queue
        g_snprintf(elem_name, sizeof(elem_name), "video_convert_queue%d", 0);
        auto video_convert_queue = gst_element_factory_make("queue", elem_name);
        GST_ELEMENT_CHECK(video_convert_queue, elem_name);
        elementBin.linkNext(video_convert_queue);
#endif
		// encode
		GST_ELEMENT_INDEX_NAME(elem_name, "sink_enc", channel);
		auto sink_enc = gst_element_factory_make(AI_ENCODER, elem_name);
		GST_ELEMENT_CHECK(sink_enc, elem_name);
		if (strcmp(AI_ENCODER, "x264enc") == 0)
		{
			if (framerate <= 0)
				framerate = 25;

			g_object_set(G_OBJECT(sink_enc), "speed-preset", 3, NULL);
			g_object_set(G_OBJECT(sink_enc), "key-int-max", framerate, NULL);		//default auto
			g_object_set(G_OBJECT(sink_enc), "bitrate", SETTINGS->bitrate(), NULL);			//default 2048
			g_object_set(G_OBJECT(sink_enc), "byte-stream", true, NULL);		//avc is not used for net
			g_object_set(G_OBJECT(sink_enc), "insert-vui", false, NULL);		//vui is unnecessary 
		}
		elementBin.linkNext(sink_enc);

		GST_ELEMENT_INDEX_NAME(elem_name, "sink_enc_caps_filter", channel);
		auto sink_enc_caps_filter = gst_element_factory_make("capsfilter", elem_name);
		GST_ELEMENT_CHECK(sink_enc_caps_filter, elem_name);

		GstCaps* enc_caps = gst_caps_new_simple("video/x-h264", "profile", G_TYPE_STRING, "baseline", NULL);
		g_object_set(G_OBJECT(sink_enc_caps_filter), "caps", enc_caps, NULL);
		elementBin.linkNext(sink_enc_caps_filter);

#ifndef NVIDIA
		/// h264Parse
		GST_ELEMENT_INDEX_NAME(elem_name, "encode_h264parse", channel);
		auto h264parse = gst_element_factory_make("h264parse", elem_name);
		GST_ELEMENT_CHECK(h264parse, elem_name);
		elementBin.linkNext(h264parse);
#endif

		// rtmp flvmux
		GST_ELEMENT_INDEX_NAME(elem_name, "sink_rtmp_flvmux", channel);
		GstElement* sink_rtmp_flvmux = gst_element_factory_make("flvmux", elem_name);
		GST_ELEMENT_CHECK(sink_rtmp_flvmux, elem_name);
		g_object_set(G_OBJECT(sink_rtmp_flvmux), "streamable", TRUE, NULL);
		elementBin.linkNext(sink_rtmp_flvmux);

		// sink rtmp queue
		GST_ELEMENT_INDEX_NAME(elem_name, "sink_rtmp_queue", channel);
		auto sink_rtmp_queue = gst_element_factory_make("queue", elem_name);
		GST_ELEMENT_CHECK(sink_rtmp_queue, elem_name);
#ifndef NVIDIA
		g_object_set(G_OBJECT(sink_rtmp_queue), "leaky", 2, NULL);
		g_object_set(G_OBJECT(sink_rtmp_queue), "flush-on-eos", true, NULL);
#endif
		elementBin.linkNext(sink_rtmp_queue);

		// rtmpsink
		GST_ELEMENT_INDEX_NAME(elem_name, "sink_rtmpsink", channel);
		GstElement* sink_rtmpsink = gst_element_factory_make("rtmpsink", elem_name);
		GST_ELEMENT_CHECK(sink_rtmpsink, elem_name);
		gchar rtmp_location[100];
		guint streamId = 10000 + channel + ((processId-1) * totalChannelSize);
		g_snprintf(rtmp_location, sizeof(rtmp_location), "rtmp://127.0.0.1/live/%d live=1", streamId);
		//g_print("rtmp_location %s\n", rtmp_location);
		g_object_set(G_OBJECT(sink_rtmpsink), "location", rtmp_location, "async", FALSE, "sync", FALSE, NULL);
		elementBin.linkNext(sink_rtmpsink);

		NVGSTDS_BIN_ADD_GHOST_PAD(encode_bin, elementBin.first, "sink");

		return encode_bin;
	}
}

