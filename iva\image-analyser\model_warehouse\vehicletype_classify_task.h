#pragma once
#include "class_task_base.h"

namespace ia
{
    /**
     * 车辆类型分类器行为类
     */
    class VehicleTypeClassifyAction : public ClassifierActionBase {
    public:
        explicit VehicleTypeClassifyAction(int deviceID = 0) 
            : ClassifierActionBase(deviceID, 5, "vehicletype") {}
    };

    /**
     * 车辆类型分类器管理类
     */
    using VehicleTypeClassifyMgr = ClassifierManagerBase<VehicleTypeClassifyAction>;
} 