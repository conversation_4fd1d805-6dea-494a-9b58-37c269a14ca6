#pragma once
#include "base_video_record.h"

namespace record
{

    enum class EvtRecordType
    {
        FVM = 0,
        IVA
    };

    /**
     * @brief 事件录像相关参数
     * @note 由于VideoRecordBase不能设置录像文件名，需要在调用NvDsSRStart后从NvDsSRContext的filesink插件中获取地址,
     *       所以事件不能同步上报，需等到start后调用回调eventRecordCb，带上EvtVideoRecordTask字段参数，上报事件协议
     */
    struct EvtVideoRecordTask
    {
        std::chrono::system_clock::time_point startTime;   //!< 事件发生事件
        int channelId;
        std::string videoPath;
        std::string imgPath;
        evt::EventInfo eventInfo;
        network::AlarmType alarmType;
        network::Area_t objectArea;
        uint64_t totalDuration;
        std::deque<EvtVideoRecordTask> eventTargets;        //!< 同一录像时间段的事件目标，即在该录制视频时间区间内的其他事件目标，这些目标不用录像，只记录在录像的时间偏移点，并上报
    };

    /**
     * @brief 录像开始回调，用于创建目标发生事件的初始框子信息，相对于录制视频的时间位置信息
     * @param[in] duration: 视频录制的总时长 s
     * @param[in] position: 目标在视频中的位置(ms)
     * @param[in] evtInfo:  事件信息
     */
    using EventRecordCB = std::function<void(int duration, int position, const EvtVideoRecordTask &evtInfo)>;

    using EventRecordCompletedCB = std::function<void(std::string, int, int, int)>;

    /**
     * @brief 事件录像
     */
    class EventVideoRecord : public VideoRecordBase
    {

    public:
        explicit EventVideoRecord(guint channel, guint before, guint duration) : VideoRecordBase(channel, before, duration){};
        ~EventVideoRecord() override = default;

        /**
         * @brief 录像完成回调
         */
        void setCompleted(guint realDuration) override;

        /**
         * @brief 注册录像开始回调，主要用于返回录像的文件名，并带上EvtVideoRecordTask参数，执行相关用户相关业务逻辑
         */
        void registerEvtRecordCallback(EventRecordCB func);

        /**
         * @brief 注册录像开始回调，主要用于返回录像的文件名，并带上EvtVideoRecordTask参数，执行相关用户相关业务逻辑
         */
        void registerEvtRecordCompletedCallback(EventRecordCompletedCB func);

        /**
         * @brief 提交录像任务
         */
        bool submit(EvtVideoRecordTask task);

        inline EvtRecordType getRecordType(){return evtRecordType;};

        inline void changeRecordType(EvtRecordType type){evtRecordType = type;};

    private:
        EventRecordCB eventRecordCb;                                    //!< 录像开始回调
        EventRecordCompletedCB eventRecordCompletedCb;
        EvtVideoRecordTask currentRecord;                               //!< 该通道当前正在录制的任务
        std::vector<EvtVideoRecordTask> evtVideoRecordTasks;            //!< 通道对应的录像任务
        EvtRecordType evtRecordType = EvtRecordType::IVA;
    };

    /**
     * 创建对应通道的录像context
     * @param[in] index   pipeline序号
     * @param[in] params  录像配置参数
     */
    void createEventRecord(guint index, guint before, guint after, const std::string& relativePath,  NvDsSRInitParams params);
    /**
     * 获取对应通道的录像context
     * @param[in] index       通道号
     */
    std::shared_ptr<EventVideoRecord> getEventVideoRecord(guint index);
}