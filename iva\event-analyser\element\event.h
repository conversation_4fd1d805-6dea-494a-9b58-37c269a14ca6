/**
 * Project AI事件分析模块
 */


#ifndef _EVENT_H
#define _EVENT_H

#include <memory>
#include <string>
#include <list>
#include "element/rect.h"
#include "element/event_info.h"
#include "enum/event_type.h"
#include "enum/target_type.h"
#include "enum/event_state.h"
#include "map/scene_object.h"
#include "element/target.h"
#include "opencv2/core.hpp"
#include "log.h"

 /**
 *
 * 事件
 */
namespace evt
{
	using std::string;
	class EventDetector;
	class Event : public SceneObject{
	public:
		friend class EventDetector;	
		friend class EventMap;
		friend class Scene;
		friend class Area;	// TODO
		Event();
		/**
		 * @param id
		 * @param type
		 * @param state
		 * @param rect
		 */
		Event(string id, EventType type, EventState state, Rect rect);

		/**
		 * 事件ID
		 */
		string getID() { return info.id; };

		/**
		 * 事件ROI ID
		 */
		int getROIID() { return info.roiID; };

		/**
		 * 事件类型
		 */
		EventType getType() { return info.type; };

		/**
		 * 事件状态
		 */
		EventState getState() { return info.state; };

		/**
		 * 事件确认子状态
		 */
		EventConfirmState getConfirmState() { return confirmState; };

		/**
		 * 关联目标ID
		 */
		int getTargetID() { return info.targetID; };

		/**
		 * 获取关联目标类型
		 */
		TargetType getTargetType() { return info.targetType; };

        /**
         * 获取关联目标分类类型 key: classifierType value: label
         */
        std::map<std::string, std::string> getTargetClassType() { return info.targetClassTypes; };

		/**
		 * 事件发生时间
		 */
		long long getOccurTime() { return info.occurTime; };

		/**
		 * 事件移除时间
		 */
		long long getRemoveTime() { return info.removeTime; };

		/**
		 * 事件发生位置框
		 */
		Rect getOccurRect() { return info.occurRect; };

		/**
		 * 获取状态持续时间（秒）
		 */
		float getStateLife() { return stateLife; };

		/**
		 * 切换事件状态
		 * @param state
		 */
		void setState(EventState state);

		/**
		 * 设置事件子状态
		 * @param state
		 */
		void setConfirmState(EventConfirmState state);

		/**
		 * 设置发生时间
		 * @param t
		 */
		void setOccurTime(long long t);

		/**
		 * 设置移除时间
		 * @param t
		 */
		void setRemoveTime(long long t);

		/**
		 * 增加状态持续时间（秒）
		 * @param life
		 */
		void addStateLife(float life);
		/**
		 * 设置状态持续时间（秒）
		 * @param life
		 */
		void setStateLife(float life);

		/**
		 * 更新事件坐标框
		 * @param r
		 */
		void updateOccurRect(Rect r);

		/**
		 * 更新事件区域 多边形框 (拥堵显示子区域)
		 * @param p
		 */
		void updateOccurArea(Polygon p);

		/**
		 * 更新关联的所有矩形
		 */
		void updateExtraRect(std::vector<Rect> rts);

		//插入额外矩形
		void insertExtraRect(Rect rt);

		/**
		 * 更新事件图像特征
		 * @param mat
		 */
		void updateThumb(cv::Mat mat);

		/**
		 * 区间检查计数
		 */
		void setPeriodCheckCount(int val);
		int getPeriodCheckCount();

		/**
		 * 区间计数通过
		 */
		void setPeriodPassCount(int val);
		int getPeriodPassCount();

		/**
		 * 设置事件检查（影响）额外距离
		 * @param r
		 */
		void setPadding(float pad);

		/**
		 * 获取当前外接矩形框 (通过 SceneObject 继承)
		 */
		Rect bound() override;

		/**
		 * 设置事件目标
		 */
		void setTarget(TargetPtr t);
		/**
		 * 获取事件目标
		 */
		TargetPtr getTarget();

		/**
		 * 事件是否被忽略
		 */
		bool isIgnored() { return ignored; }

		/**
		 * 设置平均车速  (km/h)
		 */
		void setAverageSpeed(float speed) { info.averageSpeed = speed; }

		/**
		 * 设置事件扩展信息
		 */
		void setExtInfo(std::string ext) { info.ext1 = ext; }

		/**
		 * 设置施工扩展信息
		 */
		//void setConstructionInfo(int vehicle, int person) { info.constructionVehicles = vehicle; info.constructionPerson = person; }

	private:
		/**
		 * 事件信息
		 */
		EventInfo info;

		/**
		 * 事件所属目标
		 */
		TargetPtr target;

		/**
		 * 事件确认子状态
		 */
		EventConfirmState confirmState;
		
		/**
		 * 时间发生时图像信息（若对比模块开启）
		 */
		cv::Mat occurThumb;
		/**
		 * 区间检查计数
		 */
		int periodCheckCount;
		/**
		 * 区间通过计数
		 */
		int periodPassedCount;
		/**
		 * 当前状态维持秒数
		 */
		float stateLife;
		/**
		 * 事件区域额外检查距离
		 */
		float padding;
		/**
		 * 检出的检测器
		 */
		EventDetector* detector;
		/**
		 * 是否忽略
		 */
		bool ignored;
};

	typedef std::shared_ptr<Event> EventPtr;
	typedef std::list<EventPtr> EventList;
}
#endif //_EVENT_H
