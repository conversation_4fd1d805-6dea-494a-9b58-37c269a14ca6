#include <iostream>
#include <thread>
#include <future>
#include <chrono>
#include "protocol_sender.h"
#include "protocol_manager.h"
#include "protocol_utility.h"
#include "ivautils.h"

namespace iva::protocol
{
    using namespace network;
    using namespace network::util;
    using namespace std;

    atomic_bool keepAliveThreadExit = false;
    std::thread keepAliveThread;
    std::once_flag initAliveTaskOnce;
    int processId = 1;                     //!< iva GPU设备ID
    int channelOffsetVal = 0;              //!< pipeline序号到检测通道号的转换偏移值
    auto lastInitTime = std::chrono::system_clock::now();  // 上次发送初始化消息时间
    uint8_t initInterval = 10;              // 发送初始化消息时间间隔

    // 初始化消息接收状态
    struct InitMessageStatus
    {
        uint8_t MAX_RESEND_COUNT = 3;            // 最大重发次数
        uint8_t currentResendCount = 0;           // 当前重发次数
        atomic_bool isReinitRequesting = false;
        bool systemConfigReceived = false;
        bool algorithmParamReceived = false;
        bool basicConfigReceived = false;
        bool channelDetectReceived = false;
        std::string lastMissingMsgs = {};           // 上次缺失的消息列表

        bool allReceived() const
        {
            return systemConfigReceived && algorithmParamReceived && basicConfigReceived && channelDetectReceived;
        }

        void reset()
        {
            systemConfigReceived = false;
            algorithmParamReceived = false;
            basicConfigReceived = false;
            channelDetectReceived = false;
            lastMissingMsgs.clear();
            currentResendCount = 0;
            isReinitRequesting = false;

        }

        std::string getMissingMsgs() const
        {
            std::string missingMsgs;
            if (!systemConfigReceived) missingMsgs += "SystemConfig,";
            if (!algorithmParamReceived) missingMsgs += "AlgorithmParam,";
            if (!basicConfigReceived) missingMsgs += "BasicConfig,";
            if (!channelDetectReceived) missingMsgs += "ChannelDetect,";
            if (!missingMsgs.empty()) missingMsgs.pop_back(); // 移除最后的逗号
            return missingMsgs;
        }
    };

    InitMessageStatus initStatus;
    std::mutex initStatusMutex;
    std::thread checkInitStatusThread;
    atomic_bool checkInitStatusThreadExit = false;


    /**
     * @brief      iva-->web心跳包
     */
    void keepAliveTask();

    /**
     * @brief 检查初始化消息状态并在必要时重发请求
     */
    void checkInitStatusTask();

    void initStatusManager()
    {
        std::call_once(initAliveTaskOnce,[&]()
        {
            keepAliveThread = std::thread([&](){
                keepAliveTask();
            });

            // 启动初始化消息状态检查线程
            checkInitStatusThreadExit = false;
            checkInitStatusThread = std::thread([&](){
                checkInitStatusTask();
            });
        });
    }

    void disposeStatusManager()
    {
        keepAliveThreadExit = true;
        checkInitStatusThreadExit = true;

        if (keepAliveThread.joinable())
            keepAliveThread.join();
        if (checkInitStatusThread.joinable())
            checkInitStatusThread.join();
    }

    /**
     * @brief      iva-->web心跳包
     */
    void keepAliveTask()
    {
        std::cout << "create aliveThread" << std::endl;
        this_thread::sleep_for(100ms);
        while ( !keepAliveThreadExit )
        {
            //! TODO 多进程情况下，web暂时不能正常显示进程状态，待后续联调测试
            PROTOCOL_MANAGER.sendToWEB(ProtocolType::HTTP_IVA_HEART_INFO, HeartInfo{UDP_START_LISTEN_PORT + processId - 1, 1});
            for ( int i = 0; i < 50; i++ )
            {
                if ( keepAliveThreadExit )
                    break;
                this_thread::sleep_for(100ms);
            }
        }
        IVA_LOG_INFO("exit aliveThread");
    }

    bool isReinitRequesting()
    {
        return initStatus.isReinitRequesting;
    }

    /**
     * @brief 检查初始化消息状态并在必要时重发请求
     */
    void checkInitStatusTask()
    {
        IVA_LOG_INFO("create checkInitStatusThread");
        this_thread::sleep_for(10s);  // 程序启动后10秒后再检查消息接收状态

        while (!checkInitStatusThreadExit)
        {
            // 每1秒检查一次初始化状态
            for (int i = 0; i < 20 && !checkInitStatusThreadExit; ++i)
                std::this_thread::sleep_for(50ms);

            if (checkInitStatusThreadExit)
                break;

            // 检查是否所有必要的初始化消息都已接收
            std::string missingMsgs;
            {
                std::lock_guard<std::mutex> lock(initStatusMutex);
                if (initStatus.allReceived())
                {
                    initStatus.isReinitRequesting = false;
                    initStatus.currentResendCount = 0; // 所有消息都已接收，重置计数
                    initStatus.lastMissingMsgs.clear(); // 清空上次缺失消息
                    continue;
                }
                missingMsgs = initStatus.getMissingMsgs();
            }

            // 检查是否需要重发请求
            auto elapsedTime = std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now() - lastInitTime).count();
            if (elapsedTime >= initInterval)
            {
                // 超过时间间隔，检查是否达到最大重发次数
                std::lock_guard<std::mutex> lock(initStatusMutex);
                if (initStatus.currentResendCount < initStatus.MAX_RESEND_COUNT)
                {
                    initStatus.isReinitRequesting = true;
                    reSendRequestInit(); // 重发请求
                    initStatus.currentResendCount++;
                    IVA_LOG_INFO("Resending init request ({}/{}), missing: {}", initStatus.currentResendCount, initStatus.MAX_RESEND_COUNT, missingMsgs);
                }
                else if (missingMsgs != initStatus.lastMissingMsgs) // 达到最大重发次数, 且缺失的消息发生了变化
                {
                    initStatus.isReinitRequesting = false;
                    IVA_LOG_WARN("Max resend attempts reached ({}), still missing: {}", initStatus.MAX_RESEND_COUNT, missingMsgs);
                    initStatus.lastMissingMsgs = missingMsgs;
                }
            }
        }
        IVA_LOG_INFO("exit checkInitStatusThread");
    }

    /**
     * @brief     请求初始化
     * @param[in] processID   进程号
     * @param[in] channelSize 当前进程支持的通道总个数
     */
    void sendRequestInit(int processID, int channelSize)
    {
        lastInitTime = std::chrono::system_clock::now();
        processId = processID;
        channelOffsetVal = 1 + (processId - 1) * channelSize;

        // 重置初始化消息状态和重发计数
        {
            std::lock_guard<std::mutex> lock(initStatusMutex);
            initStatus.reset();
        }

        RequestInit requestInit{processId};
        PROTOCOL_MANAGER.sendToFVM(ProtocolType::UDP_REQUEST_INIT, requestInit);
        IVA_LOG_INFO("[udp][send] request initialization to FVM");
    }

    void reSendRequestInit()
    {
        if (std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now() - lastInitTime).count() < initInterval) {
            return;
        }
        lastInitTime = std::chrono::system_clock::now();
        RequestInit requestInit{processId};
        PROTOCOL_MANAGER.sendToFVM(ProtocolType::UDP_REQUEST_INIT, requestInit);
        IVA_LOG_INFO("[udp][send] request initialization to FVM");
    }

    /**
     * @brief 更新系统配置消息接收状态
     */
    void updateSystemConfigStatus()
    {
        std::lock_guard<std::mutex> lock(initStatusMutex);
        initStatus.systemConfigReceived = true;
        IVA_LOG_INFO("Received SystemConfig message");
    }

    /**
     * @brief 更新算法参数消息接收状态
     */
    void updateAlgorithmParamStatus()
    {
        std::lock_guard<std::mutex> lock(initStatusMutex);
        initStatus.algorithmParamReceived = true;
        IVA_LOG_INFO("Received AlgorithmParam message");
    }

    /**
     * @brief 更新基本配置消息接收状态
     */
    void updateBasicConfigStatus()
    {
        std::lock_guard<std::mutex> lock(initStatusMutex);
        initStatus.basicConfigReceived = true;
        IVA_LOG_INFO("Received BasicConfig message");
    }

    /**
     * @brief 更新通道检测参数消息接收状态
     */
    void updateChannelDetectStatus()
    {
        std::lock_guard<std::mutex> lock(initStatusMutex);
        initStatus.channelDetectReceived = true;
        IVA_LOG_INFO("Received ChannelDetect message");
    }

    /**
     * @brief 事件解除通知
     * @param[in] eventId:      事件Id
     * @param[in] finishTime:   事件解除时的时间
     * @param[in] eventType:    事件类型
     * @param[in] imagePath:    事件对应的图片路径
     * @param[in] videoPath:    事件对应的视频路径
     */
    void postEventRemove(const std::string& eventId, const std::string& finishTime, int eventType, const std::string& imagePath, const std::string& videoPath)
    {
        EventRemoveInfo eventRemoveInfo;
        eventRemoveInfo.eventId = eventId;
        eventRemoveInfo.finishTime = finishTime;
        eventRemoveInfo.eventTypeId = eventType;
        if (!videoPath.empty())
            eventRemoveInfo.finishVideo = PROTOCOL_MANAGER.getWebURL() + videoPath;
        if (!imagePath.empty())
            eventRemoveInfo.finishImg = PROTOCOL_MANAGER.getWebURL() + imagePath;

        PROTOCOL_MANAGER.sendToWEB(ProtocolType::HTTP_EVENT_REMOVE, eventRemoveInfo);
    }

    /**
     * @brief 事件撤回通知
     * @param[in] eventId:       事件Id
     * @param[in] eventType:     事件类型
     * @param[in] occurTime:     事件发生时的时间
     */
    void postEventWithdraw(const std::string& eventId, int eventType, const std::string& occurTime)
    {
        PROTOCOL_MANAGER.sendToWEB(ProtocolType::HTTP_EVENT_WITHDRAW, EventWithdraw{eventId,eventType});
    }

    /**
     * @brief 视频质量告警
     * @param[in] videoId:       视频源Id
     * @param[in] alarmTypes:    告警类型集
     * @param[in] isPauseDetect: 是否暂停检测
     * @note      在iva.ini中配置了enableFvmOffsetFilter，则往fvm发送消息，否则往平台web发送消息
     */
    void postVideoQuaAlarm(int videoId, const std::vector<int>& alarmTypes, int isPauseDetect)
    {
        VideoQuaAlarmConf videoQuaAlarmConf{videoId, isPauseDetect, PROTOCOL_MANAGER.getLocalIP(), alarmTypes};
        auto ret = PROTOCOL_MANAGER.sendToFVM(ProtocolType::UDP_VIDEO_QUAALARM, videoQuaAlarmConf);
        IVA_LOG_INFO("[udp] report video {} quality start ret {}", videoId, (ret ? " ok" : " fail"));
    }

    /**
     * @brief 视频质量恢复
     * @param[in] videoId:       视频源Id
     * @param[in] presetId:      预置位Id
     * @param[in] restoreTypes:  视频质量恢复类型集
     */
    void postVideoQuaRecovery(int videoId, int presetId, const std::vector<int>& restoreTypes)
    {
        VideoQuaRecovery videoQuaRecovery{videoId, presetId, PROTOCOL_MANAGER.getLocalIP(), restoreTypes};
        auto ret = PROTOCOL_MANAGER.sendToFVM(ProtocolType::UDP_VIDEO_QUARECOVERY, videoQuaRecovery);
        IVA_LOG_INFO("[udp] report video {} quality stop ret {}", videoId, (ret ? " ok" : " fail"));
    }

    /**
     * @brief 通道检测状态上报
     * @param[in] index:         管道序号
     * @param[in] videoId:       视频源Id
     * @param[in] isDetect:      是否处于检测状态
     */
    void postDetectStatus(int index, int videoId, int isDetect)
    {
        if (videoId == -1)
            return;
        int channelId = index + channelOffsetVal;
        DetectStatusInfo statusInfo{channelId, videoId, isDetect};
        auto ret = PROTOCOL_MANAGER.sendToWEB(ProtocolType::HTTP_DETECT_STATUS, statusInfo);
        IVA_LOG_INFO("[http] report channel {} video {} detect status {} ret {} ", channelId, videoId, isDetect, (ret ? " ok" : " fail"));
    }

    /**
     * @brief 请求日夜切换
     * @param[in] index:         管道序号
     * @param[in] videoId:       视频源Id
     * @param[in] isDay:         请求日或夜的时间切换方案
     */
    void postDayNight(int index, int videoId, int isDay)
    {
        if (videoId == -1)
            return;
        int channelId = index + channelOffsetVal;
        auto ret = PROTOCOL_MANAGER.sendToFVM(ProtocolType::UDP_REQUEST_DAYNIGHT, RequestDayNightInfo{channelId, videoId, isDay});
        IVA_LOG_INFO("[UDP] request day night channel {} videoId {} isDay {} ret {}", channelId, videoId, isDay, (ret ? " ok" : " fail"));
    }

    /**
     * @brief 事件上报
     */
    bool postEvent(int index, const std::string& videoPath, const std::string& imgPath,
                   AlarmType alarmType, const Area_t &objectArea, const evt::EventInfo& eventInfo)
    {
        auto tet = PROTOCOL_MANAGER.getLocalIP();
        EventOccurInfo evtOccurInfo;
        evtOccurInfo.channelId = index + channelOffsetVal;
        evtOccurInfo.roiId = eventInfo.roiID;
        evtOccurInfo.laneId = eventInfo.laneID;
        evtOccurInfo.checkAreaId = eventInfo.regionID;
        evtOccurInfo.eventTypeId = eventInfo.type;

        std::stringstream classTypes;
        for (auto itr = eventInfo.targetClassTypes.begin(); itr != eventInfo.targetClassTypes.end(); ++itr)
        {
            classTypes << itr->first << ":" << itr->second;
            if (std::next(itr) != eventInfo.targetClassTypes.end())
                classTypes << ",";
        }
        evtOccurInfo.eventSubType = classTypes.str();

        evtOccurInfo.occurTime = get_system_full_time_str(eventInfo.occurTime);;
        evtOccurInfo.eventVideo = PROTOCOL_MANAGER.getWebURL() + videoPath;
        evtOccurInfo.eventImg = PROTOCOL_MANAGER.getWebURL() + imgPath;
        evtOccurInfo.isAlarm = alarmType;
        evtOccurInfo.objectArea = objectArea.vtPoint;
        evtOccurInfo.areaTypeId = 0;
        evtOccurInfo.eventId = eventInfo.id;
        PROTOCOL_MANAGER.sendToFVM(ProtocolType::UDP_EVENT_OCCURED, evtOccurInfo);

        return true;
    }


    bool postTrack(std::vector<Trackinfo>& tracks )
    {
        if (tracks.empty())
            return false;

        PROTOCOL_MANAGER.sendToWEB(ProtocolType::HTTP_SAVE_DETECTINFO, VecDetectInfo{{DetectInfo{std::move(tracks)}}});
        return true;
    }

    /**
     * @brief 上报事件录像完成(IVA录像)消息
     */
    bool postEventVideoFinished(std::string videoWebUrl)
    {
        VideoFinished eventInfo;
        eventInfo.eventVideo = std::move(videoWebUrl);
        PROTOCOL_MANAGER.sendToWEB(network::ProtocolType::HTTP_EVENT_VIDEO_FINISHED, eventInfo);
        return true;
    }

    /**
     * @brief 上报车辆信息
     * @param[in] vehicleInfo:   车辆信息
     */
     void postVehicleInfo(const std::vector<network::VehicleInfo>& vehicleInfo)
     {
         for (auto& info : vehicleInfo)
         {
             if (!info.ObjectRects.empty())
             {
                 if (!PROTOCOL_MANAGER.sendToWEBViaUDP(ProtocolType::UDP_VEHICLE_INFO, info))
                     IVA_LOG_ERROR("[udp] report vehicle info  fail");
             }
         }
     }

}
