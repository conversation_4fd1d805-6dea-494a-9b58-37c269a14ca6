/**
 * Project AI事件分析模块
 */

#include "target.h"
#include "event.h"
#include "util/scene_utility.h"
#include "area/area.h"
#include "area/lane.h"
#include "area/region.h"
#include "area/roi.h"

/**
 * Target implementation
 * 
 * 目标对象
 */
namespace evt
{
	/**
	 * @param id
	 * @param type
	 */
	Target::Target(int _id, TargetType _type, const std::map<string, string>& classTypes){
		this->info.id = _id;
		this->info.type = _type;
		this->info.speed = 0;
		this->info.eventType = EventType_None;
		this->info.createTime = systemTimestamp();

		this->trackIndex = 0;
		this->life = 0;
		this->turnIndex = 0;
		this->oppositeIndex = 0;
		this->matched = false; 
		this->removed = false; 
		this->tracked = false;
		this->passedROI = false;
		this->sceneflowChecked = false;
		this->countLineState = TargetCountLineState_None;

		this->currentArea = NULL;
		this->oppositeCount = 0;
		this->averageSpeedRatio = 0;

		this->countSpeed = false;

        updateClassType(classTypes);
	}

	/**
	 * 更新轨迹帧
	 * @param track
	 */
	void Target::updateTrack(Track& track) {

		// 更新轨迹帧
		track.index = trackIndex++;
		info.tracks.push_back(track);

		if (info.tracks.size() > TARGET_TRACK_MAX_LENGTH)  // 截断track 防止过长
		{
			info.tracks.erase(info.tracks.begin(), info.tracks.begin() + TARGET_TRACK_ADJUST_LENGTH);
		}

		// 更新目标方向向量
		int trackSize = getTrackCount();
		if (trackSize > 1)
		{
			int meanSize = std::min(TARGET_VELOCITY_MEAN_INDEX, trackSize);

			Vector2D instantVelocity(getLatestPos(1, true), getLatestPos(0, true));
			this->accumulatedVelocity += instantVelocity;

			updateSpeedRatio(instantVelocity.length());

			if (trackSize > TARGET_VELOCITY_MEAN_INDEX)
			{
				Vector2D lastVelocity(getLatestPos(TARGET_VELOCITY_MEAN_INDEX, true), getLatestPos(TARGET_VELOCITY_MEAN_INDEX-1, true));
				this->accumulatedVelocity -= lastVelocity;
			}

			// 检查目标冻结
			if (instantVelocity.length() < 2.f)
			{
				if (frozenCount > 0 || (frozenCount == 0 && this->averageSpeedRatio > 0.03f))
				{
					frozenCount++;
				}
			}
			else
			{
				frozenCount = 0;
			}

			this->velocity = this->accumulatedVelocity / (meanSize- 1);

			// 检查静止目标
			if (this->accumulatedVelocity.length() > 10.f)
				initialStill = false;
		}
	}

	/**
	 * 更新区域信息
	 * @param region
	 */
	void Target::updateArea(Area* area, int roiID) {
		if (passedAreas.size() == 0 
			|| (passedAreas.size() > 0 && passedAreas.back().area != area))
		{
			if (this->info.roiID > 0 && this->info.roiID != roiID)
			{
				this->passedROI = true;
			}
			this->info.roiID = roiID;

			AreaPassInfo passInfo{ trackIndex , area , getLatestRect()};
			passedAreas.emplace_back(passInfo);
		}
	}
	/**
	 * 更新区域信息
	 * @param area 当前区域
	 * @param bSetCurArea
	 * @param bAddArea 是否加入到当前区域中
	 */
	void Target::updateArea(Area* area, bool bSetCurArea, bool bAddArea)
	{
		if (!area)
		{
			//this->info.roiID = 0;
			this->info.laneID = 0;
			this->info.regionID = 0;
			return;
		}
		auto lane = dynamic_cast<Lane*>(area);
		auto region = dynamic_cast<Region*>(area);
		auto roi = dynamic_cast<ROI*>(area);
		if (lane)
		{
			this->info.laneID = lane->getID();
			this->info.laneType = lane->getLaneType();
		}
		else if (region)
		{
			this->info.regionID = region->getID();
		}
		else if (roi)
		{
			if (getLatestRect().getSideLength()> 100)
			{
				if (this->info.roiID > 0 && this->info.roiID != roi->getID())
				{
					this->passedROI = true;
				}
			}
			this->info.roiID = roi->getID();
			this->info.roiArea = roi->getFramePolygon();
		}
		else
		{
			this->info.roiID = area->getID();
		}

		if (bSetCurArea)
		{
			this->currentArea = area;
		}

		if (bAddArea)
		{
			if (passedAreas.size() == 0
				|| passedAreas.back().area != area)
			{
				AreaPassInfo passInfo{ trackIndex , area , getLatestRect() };
				passedAreas.emplace_back(passInfo);
			}
		}
	}

	/**
	 * 更新车道信息
	 * @param lane
	 */
	void Target::updateLane(Lane * lane)
	{
		if (lane)
		{
			info.laneID = lane->getID();
			info.laneType = lane->getLaneType();
		}
		else
		{
			info.laneID = 0;
			info.laneType = LaneType_None;
		}
	}
	/**
	 * 更新区域信息
	 * @param lane
	 */
	void Target::updateRegion(Region* region)
	{
		if (region)
			info.regionID = region->getID();
		else
			info.regionID = 0;
	}

	/**
	 * 更新关联事件信息
	 * @param evt
	 */
	void Target::updateEvent(Event* evt) {
		if (evt)
		{
			this->info.eventID = evt->getID();
			this->info.eventState = evt->getState();
			this->info.eventType = evt->getType();
            this->updateClassType(evt->getTargetClassType());
			this->info.eventOccurTime = evt->getOccurTime();
		}
		else
		{
			this->info.eventID = "";
			this->info.eventState = EventState_None;
			this->info.eventType = EventType_None;
			this->info.eventOccurTime = 0;
		}
	}

	/**
	 * 更新过线状态
	 * @param state
	 */
	void Target::updateCountLineState(TargetCountLineState state)
	{
		countLineState = state;
	}

	/**
	 * 更新速度(Km/h)，且停止计算车速
	 */
	void Target::updateSpeed(int val)
	{
		this->info.speed = val;
		countSpeed = false;
	}

	/**
	* @brief    更新像素比例平均速度 （像素位移/框对角线）
	* param[in] len:相邻两帧的像素位移长度
	*/
	void Target::updateSpeedRatio(float len)
	{
		if (getTrackCount() <= 1)
		{
			return;
		}

		float instantSpeed = len / ((getLatestRect(1).getDiagonalLen() + getLatestRect(0).getDiagonalLen()) / 2);
		normalInstantSpeeds.push_back(instantSpeed);

		if ((normalInstantSpeeds.size() > 1) && (getTrackCount() > 2))
		{
			Vector2D lastInstantVelocity(getLatestPos(2, true), getLatestPos(1, true));		  
			float lastInstantSpeed = lastInstantVelocity.length() / ((getLatestRect(2).getDiagonalLen() + getLatestRect(1).getDiagonalLen()) / 2);
			if ((instantSpeed > TARGET_ABNORMAL_SPEEDRATIO_COEFF * lastInstantSpeed) && (lastInstantSpeed > 0.001))	  ///< 过滤瞬时突变速度
			{
				normalInstantSpeeds.pop_back();
			}
		}

		if (normalInstantSpeeds.size() > TARGET_VELOCITY_MEAN_INDEX)
		{
			normalInstantSpeeds.pop_front();
		}
		std::deque<float> sortInstantSpeeds = normalInstantSpeeds;
		std::sort(sortInstantSpeeds.begin(), sortInstantSpeeds.end(), std::less<float>()); ///< 瞬时速度排序

		float  sumSpeed = 0;
		uint16_t speedCount = 0;
		uint16_t totalLen = sortInstantSpeeds.size();
		for (uint16_t i = 0; i < totalLen; i++)		 ///< 去掉排序后的前后1/4的速度
		{
			if (((totalLen >= 3) && (i > 0.25 * (totalLen - 1)) && (i < 0.75 * (totalLen - 1)))
				|| (totalLen < 3))
			{
				speedCount++;
				sumSpeed += sortInstantSpeeds[i];
			}
		}
		this->averageSpeedRatio = (speedCount == 0) ? 0 : sumSpeed / speedCount;
	}

	/**
	* @brief  获取目标框移动的平均速度 （像素位移/框对角线）
	*/
	float Target::getAverageSpeedRatio()
	{
		return averageSpeedRatio;
	}

	/**
	* @brief  是否需要计算车速，当车速为0，过线，轨迹点大于10，才可计算车速
	*/
	bool Target::needCountSpeed()
	{
		if (!countSpeed || getSpeed() > 0 || getTrackCount() < TARGET_SPEED_TRACK_MIN * 2 )
			return false;
		return true;
	}

	/**
     *    设置开始计算车速
     */
	void Target::startCountSpeed()
	{
		countSpeed = true;
	}


	/**
	 * 更新折点（拐点）序号
	 */
	void Target::updateTurnIndex(Vector2D direction, float turnAngle)
	{
		float speed = this->velocity.dot(direction);
		float width = getLatestRect().getMaxLength();
		float speedRatio = std::abs(speed) / width;
		if (speedRatio > 0.0002f)
		{
			if (this->turnVelocities.empty())
			{
				turnIndex = trackIndex;
				oppositeIndex = trackIndex;
				this->turnVelocities.push_back(this->velocity);
				this->oppositeIndexs.push_back(oppositeIndex);
			}
			else
			{
				auto checkPos = getIndexPos(oppositeIndex, true);
				auto curPos = getLatestPos(0, true);
				auto vehicleAngle = Vector2D(checkPos, curPos).angle(direction);
				if (vehicleAngle > turnAngle)
				{
					oppositeIndex = trackIndex;
					this->oppositeIndexs.push_back(oppositeIndex);
				}
				else if (std::abs(vehicleAngle) < 10 && speedRatio > 0.01f)
				{
					if (!this->oppositeIndexs.empty())
					{
						this->oppositeIndexs.pop_back();
					}
				}
			}
		}

		if (!this->oppositeIndexs.empty())
		{
			int passed = trackIndex - this->oppositeIndexs.front();
			if (passed > TARGET_OPPOSITE_TRACK_SLIDE || this->oppositeIndexs.size() > TARGET_OPPOSITE_TRACK_SLIDE)
			{
				this->oppositeIndexs.pop_front();
			}

			passed = trackIndex - turnIndex;
			if (passed > TARGET_TURN_TRACK_MAX)
			{
				turnIndex = trackIndex - TARGET_TURN_TRACK_MAX;
			}
		}
	}

	/**
	 * 更新额外信息 (osd 显示)
	 */
	void Target::updateExtraData(std::string data)
	{
		this->info.extraData = data;
	}

	/**
	 * 设置相关事件类型
	 * @param evtType
	 */
	void Target::addRelatedEventType(EventType evtType)
	{
		this->relatedEventTypes.set(evtType);
	}

	/**
	 * 是否和事件类型关联（ROI范围事件）
	 * 即，是否参与 范围事件（拥堵、事故、施工等） 的计算
	 * @param evtType
	 */
	bool Target::hasRelatedEventType(EventType evtType)
	{
		return this->relatedEventTypes.contains(evtType);
	}

	/**
	 * 清理关联事件类型
	 */
	void Target::clearRelatedEventTypes()
	{
		this->relatedEventTypes.reset();
	}

	/**
	 * 获取折点（拐点）序号
	 */
	int Target::getTurnIndex()
	{
		return turnIndex;
	}

	/**
	 * 是否穿越ROI
	 */
	bool Target::IsPassedROI()
	{
		return passedROI;
	}

	/**
	 * 目标ID
	 * @return int
	 */
	int Target::getID() {
		return this->info.id;
	}

	/**
	 * 目标类型
	 * @return TargetType
	 */
	TargetType Target::getType() {
		return this->info.type;
	}

	/**
	 * 关联事件ID
	 * @return int
	 */
	std::string Target::getEventID() {
		return this->info.eventID;
	}

	/**
	 * 关联事件状态
	 * @return EventState
	 */
	EventState Target::getEventState() {
		return this->info.eventState;
	}

	EventType Target::getEventType()
	{
		return this->info.eventType;
	}

	/**
	 * ROI ID
	 *
	 * @return int
	 */
	int Target::getROIID() {
		return this->info.roiID;
	}

	/**
	 * 车道类型
	 * @return LaneType
	 */
	LaneType Target::getLaneType() {
		return this->info.laneType;
	}

	/**
	 * 轨迹长度
	 */
	int Target::getTrackCount()
	{
		return static_cast<int>(this->info.tracks.size());
	}

	/**
	 * 获取速度
	 */
	int Target::getSpeed()
	{
		return this->info.speed;
	}

	/**
	 * 区域ID
	 * @return int
	 */
	int Target::getRegionID() {
		return this->info.regionID;
	}
	/**
	 * 车道ID
	 * @return int
	 */
	int Target::getLaneID() {
		return this->info.laneID;
	}
	/**
	 * 获取最近坐标框
	 */
	Rect Target::getLatestRect(int index)
	{
		int trackLength = static_cast<int>(this->info.tracks.size());
		if (trackLength == 0) return Rect();

		int arrIndex = trackLength - index - 1;
		arrIndex = std::min(trackLength - 1, arrIndex);
		arrIndex = std::max(0, arrIndex);
		return this->info.tracks[arrIndex].rect;
	}

	/**
	 * 获取最近坐标点
	 */
	Point Target::getLatestPos(int index, bool useCenter)
	{
		return useCenter ? getLatestRect(index).getCenterPosition() :getLatestRect(index).getPosition(info.registX, info.registY);
	}

	/**
	 * 获取最近经过区域
	 */
	bool Target::getLatestArea(AreaPassInfo& passInfo, int index)
	 {
		 int areaLength = static_cast<int>(this->passedAreas.size());
		 if (areaLength == 0) return false;

		 int arrIndex = areaLength - index - 1;
		 if (arrIndex >= 0 && arrIndex < areaLength)
		 {
			 passInfo = this->passedAreas[arrIndex];
			 return true;
		 }
		 else
		 {
			 return false;
		 }
	 }

	/**
	 * 基于帧序获取坐标框
	 */
	Rect Target::getIndexRect(int index)
	{
		int trackLength = static_cast<int>(this->info.tracks.size());
		if (trackLength == 0) return Rect();

		int offset = trackIndex - trackLength;
		int arrIndex = index - offset;

		arrIndex = std::min(trackLength - 1, arrIndex);
		arrIndex = std::max(0, arrIndex);
		return this->info.tracks[arrIndex].rect;
	}

	/**
	 * 基于帧序获取坐标点
	 */
	Point Target::getIndexPos(int index, bool useCenter)
	{
		return useCenter? getIndexRect(index).getCenterPosition() : getIndexRect(index).getPosition();
	}

	/**
	 * 获取计数线状态
	*/
	TargetCountLineState Target::getCountLineState()
	{
		return countLineState;
	}

	/**
	 * 获取当前外接矩形框 (通过 SceneObject 继承)
	 */
	Rect Target::bound()
	{
		return getLatestRect(); //  +offset;
	}

	/**
	 * 平均方向向量(像素)
	 */
	Vector2D Target::getVelocity()
	{
		return velocity;
	}

	/**
	 * 累计平均方向向量(像素)
	 */
	Vector2D Target::getAccuVelocity()
	{
		return accumulatedVelocity;
	}

	/**
	 * 拐点时刻平均方向向量(像素)
	 */
	Vector2D Target::getTurnVelocity(int latestIndex)
	{
		int len = static_cast<int>(this->turnVelocities.size());
		if (len == 0) return Vector2D();

		int arrIndex = len - latestIndex -1;
		arrIndex = std::min(len - 1, arrIndex);
		arrIndex = std::max(0, arrIndex);
		return this->turnVelocities[arrIndex];
	}

	/**
	 * 是否显示
	 */
	bool Target::getShowable()
	{
		return info.showable;
	}

	/**
	 * 逆行数量统计
	*/
	int Target::getOppositeCount()
	{
		return this->oppositeIndexs.size();
	}

	/**
	 * 获取track序号
	*/
	int Target::getTrackIndex()
	{
		return trackIndex;
	}

	/**
	 * 是否为机动车
	 */
	bool Target::isVehicleType() {
		return info.type == TargetType::TargetType_Car
			|| info.type == TargetType::TargetType_Bus
			|| info.type == TargetType::TargetType_Truck;
	}

	/**
	 * 获得当前所在区域
	 */
	Area* Target::getCurrentArea()
	{
		return this->currentArea;
	}

    /**
     * 从分类类型信息集中查找是否有某类标签类别
     * @param[in] label 判断是否有该标签类型
     */
    bool Target::hasClassType(std::string_view label)
    {
        auto targetClassTypes = info.classTypes.getClassTypes();
        auto it = std::find_if(targetClassTypes.begin(), targetClassTypes.end(), [&](const std::pair<std::string, std::string>& classInfo){
            return classInfo.second == label;
        });

        return (it != targetClassTypes.end());
    }
}
