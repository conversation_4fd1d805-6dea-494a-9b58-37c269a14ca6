#pragma once
#include "ini/inibase.h"

/*
 * 烟火 检测配置 (INI)
 *
 *    (属性类型, 属性名称, 组名称, 默认值, "注释")
 *   属性类型支持：int bool string double
 */
DEFINE_INI_CONFIG(ia::firesmoke, "config/fire_smoke.ini",
(int, preprocessFrame, filter, 10, u8"预存帧数")
(int, distanceAtLeast, filter, 10, u8"检出框中心点阈值")
(bool, enablePostprocess, filter, true, u8"是否开启后处理,默认为true")
(double, maxVariance, filter, 5, u8"框变化的最大方差值")
(int, minSmokeNum, filter, 1, u8"最小烟火数量")
#ifdef CAMBRICON_MLU370
(int, minTrackedCount, filter, 4, u8"框最小跟踪数量")
(double, minVariance, filter, 0.25, u8"框变化的最小方差值")
#else
(int, minTrackedCount, filter, 7, u8"框最小跟踪数量")
(double, minVariance, filter, 0.15, u8"框变化的最小方差值")
#endif

(int, fireMinTrackedCount, filter, 8, u8"火最小跟踪数量")
(bool, checkDirection, filter, true, u8"是否检测目标框移动方向")
)

#define FIRESMOKE_CFG ia::firesmoke::Config::instance()
