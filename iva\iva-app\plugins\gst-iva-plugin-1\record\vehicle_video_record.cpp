#include <iostream>
#include <future>
#include "vehicle_video_record.h"
#include "ivautils.h"
#include "log.h"
#include "module_option.h"
#include "ivaconfig.hpp"

namespace record
{
    using namespace std;
    using namespace std::chrono;

    std::mutex vehicleVideoRecordsLock;
    std::map<guint, std::shared_ptr<VehicleVideoRecord>> vehicleVideoRecords;           //!< 通道对应的录像context key:pipeline序号

    /**
     * 创建对应通道的录像context
     * @param[in] index   pipeline序号
     * @param[in] params  录像配置参数
     */
    void createVehicleRecord(guint index, guint before, guint after, const std::string& relativePath, NvDsSRInitParams params)
    {
        std::lock_guard lk(vehicleVideoRecordsLock);
        if (vehicleVideoRecords.find(index) == vehicleVideoRecords.end())
        {
            auto videoRecordPtr = std::make_shared<VehicleVideoRecord>(index, before, after);
            if (videoRecordPtr)
            {
                videoRecordPtr->create(params, relativePath);
                vehicleVideoRecords[index] = videoRecordPtr;
            }
        }
    }

    /**
     * 获取对应通道的录像context
     * @param[in] index       通道号
     */
    std::shared_ptr<VehicleVideoRecord> getVehicleVideoRecord(guint index)
    {
        std::lock_guard lk(vehicleVideoRecordsLock);
        return (vehicleVideoRecords.find(index) == vehicleVideoRecords.end()) ? nullptr : vehicleVideoRecords[index];
    }

    /**
     * @brief 注册录像开始回调，主要用于返回录像的文件名，并带上EvtVideoRecordTask参数，执行相关用户相关业务逻辑
     */
    void VehicleVideoRecord::registerVehicleRecordCallback(VehicleRecordCB func)
    {
        onRecordCallback = std::move(func);
    }

    /**
     * @brief 录像完成回调
     */
    void VehicleVideoRecord::setCompleted(guint realDuration)
    {
        VideoRecordBase::setCompleted(realDuration);

        if (opt::getOptionEnabled(opt::VIDEO_RECORD_OPT))
        {
            std::string logFileName = "smart_record_" + std::to_string(channel) + ".log";

            std::ofstream of(logFileName, ios::app);
            of << endl << "[record file]:" << endl;
            of << "duration:" << duration << " filename:" << fileName << endl;
            of << "position:" << beforeTime << " target:";

            for (const auto& target : currentRecord.start.targets)
                of << " id:" << target.id;

            for (auto& [startTime, targets,frame] : currentRecord.passedTargets)
            {
                int position = (int)(round<seconds>(startTime - currentRecord.start.startTime).count() + beforeTime);
                of << endl << "position:" << position << " target:";

                for (const auto& target : targets)
                    of << " id:" << target.id;
            }
            of.close();
        }

        if (!videoRecordTasks.empty())
        {
            auto future = std::async(std::launch::async,[this](){
                std::unique_lock lk(recordLock);
                guint revisedBeforeTime = 0; //!< 重新计算缓存中的beforeTime, 因为缓存重点startTime可能已经距离当前时间较长，需要更长的beforeTime
                for (auto it = videoRecordTasks.begin(); it != videoRecordTasks.end(); )
                {
                    auto now = std::chrono::system_clock::now();
                    revisedBeforeTime = (guint)round<seconds>(now - it->start.startTime).count() + beforeTime;
                    if (revisedBeforeTime > RECORD_CACHE_SIZE_SEC)   //!< 重新计算的beforeTime如果超过缓存时长，则该录像任务被延误了，无法录像
                    {
                        IVA_LOG_WARN("record task is overdue, channel {}", channel)
                        it = videoRecordTasks.erase(it);
                        continue;
                    }
                    break;
                }
                if (videoRecordTasks.empty())
                    return;

                auto task = videoRecordTasks.front();
                if (start(revisedBeforeTime, duration, gpointer(this)))
                {
                    currentRecord = task;
                    int position = revisedBeforeTime;
                    videoRecordTasks.erase(videoRecordTasks.begin());
                    if (onRecordCallback != nullptr)
                    {
                        lk.unlock();
                        onRecordCallback(fileName, position, task.start);

                        for (auto& targetInfo : task.passedTargets) //!< 如果当前视频录像时间段有其他缓存过线目标，则直接上传
                        {
                            position = (int) (round <seconds> (targetInfo.startTime - currentRecord.start.startTime).count() + revisedBeforeTime);
                            onRecordCallback(fileName, position, targetInfo);
                        }
                    }
                }
            });
        }
    }

    /**
     * @brief 提交录像任务
     */
    bool VehicleVideoRecord::submit(const PassedTargetInfo& info)
    {
        std::unique_lock lk(recordLock);
        if (recordContext->recordOn && !completed) //!< 正在录像
        {
            //!< 在时间范围内，记录当前目标的时间
            if (info.startTime < currentRecord.start.startTime + std::chrono::seconds(duration - afterTime))
            {
                if (opt::getOptionEnabled(opt::VIDEO_RECORD_OPT))
                    currentRecord.passedTargets.emplace_back(info); //!< 用于日志记录每个目标的录制时间点，调试用

                if (onRecordCallback != nullptr)
                {
                    int position = (int) (round <seconds> (info.startTime - currentRecord.start.startTime).count() + beforeTime);
                    lk.unlock();
                    onRecordCallback(fileName, position, info);
                }
                return true;
            }
            else  //!< 不在录像时间范围内，停止当前录像，准备从新开始新的录像
            {
                if (NvDsSRStop(recordContext, 0) != NVDSSR_STATUS_OK)
                    IVA_LOG_ERROR ("Unable to stop recording, channel {}", channel)

                if (videoRecordTasks.empty())
                    videoRecordTasks.emplace_back(VehicleVideoRecordTask{info,{}});
                else
                    videoRecordTasks.front().passedTargets.emplace_back(info); //!< 已有录像任务，新的目标添加到当前录像任务的“只上报”队列中
            }
        }
        else  //!< 没有录像
        {
            if (videoRecordTasks.empty())
            {
                if (start(beforeTime, duration, gpointer(this)))
                {
                    currentRecord = VehicleVideoRecordTask{info, {}};
                    int position = beforeTime;
                    lk.unlock();
                    if (onRecordCallback != nullptr)
                        onRecordCallback(this->fileName, position, info);
                }
            }
            else
            {
                videoRecordTasks.front().passedTargets.emplace_back(info);
            }
        }
        return false;
    }
}
