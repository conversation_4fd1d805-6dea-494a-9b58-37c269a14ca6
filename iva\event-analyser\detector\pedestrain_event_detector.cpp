/**
 * Project AI事件分析模块
 */

#include "pedestrain_event_detector.h"
#include "util/scene_utility.h"
#include "config/event_config.hpp"

/**
 * PedestrainEventDetector implementation
 * 
 * 行人检测器
 */
namespace evt
{
	/**
	 * 检查目标类型
	 * @param type
	 * @return bool
	 */
	bool PedestrainEventDetector::checkTargetType(TargetType type) {
		return type == TargetType::TargetType_Pedstrain;
	}

    /**
     * 检查目标分类类型
     * @param target
     */
    bool PedestrainEventDetector::checkTargetClassType(TargetPtr target)
    {
        if (!target)
            return false;

        if (!EVENT_CFG->postSafetyVestPersonEvent()) //!< 如果不上报反光衣的行人
            if (target->hasClassType(PERSON_REF_LABEL))
                return false;


        return true;
    }

	/**
	 * 事件分析核心逻辑
	 */
	EventPtr PedestrainEventDetector::process(TargetPtr target)
	{
		CHECK_TARGET_TYPE(target);
		CHECK_TARGET_FRAME_COUNT(target);

		float hwRatio = target->getLatestRect().getHWRatio();
		if(hwRatio < minHWRatio)
			return NULL;

		if (checkSpeed(target, EVENT_CFG->ignoreStillPedestrain()))
			return proposeNew(target, EventState_Proposal, eventSpace);
		else
			return NULL;
	}

	/**
	 * 事件候选
	 * @param evt
	 * @param target
	 */
	void PedestrainEventDetector::onEventProposal(EventPtr evt, TargetPtr target)
	{
		evt->setPeriodCheckCount(evt->getPeriodCheckCount() + 1);
		if (matchCheck(evt, target))
		{
			if (checkSpeed(target))
			{
				evt->setPeriodPassCount(evt->getPeriodPassCount() + 1);
			}
			else
			{
				evt->setState(EventState_None);
				evt->setStateLife(0.0f);
			}
		}

		if (evt->getPeriodCheckCount() > frameRate)
		{
			evt->getPeriodPassCount() > (int)(checkRate * (float)frameRate) ? evt->addStateLife(1.f) : evt->addStateLife(-1.f);
			evt->setPeriodCheckCount(0);
			evt->setPeriodPassCount(0);
		}
		if (evt->getStateLife() > checkTime - proposeTime)
		{
			if (target != NULL)
			{
				if (checkSpeed(target) && checkTargetClassType(target))
				{
					// 距离检查
					auto occurRect = evt->getOccurRect();
					auto curRect = target->getLatestRect();

					float width = std::min(occurRect.width, curRect.width);
					float movedDis = calculateDistance(occurRect.getCenterPosition(), curRect.getCenterPosition());

					float passedTime = checkTime - proposeTime;
                    int  middleIndex = target->getTrackCount() / 2;
                    float middleDiagonal = target->getLatestRect(middleIndex).getDiagonalLen();
                    float checkDis =  (middleDiagonal * maxSpeed * passedTime) / PEDESTRAIN_SPEED_TRANS_COEFF;  //!< (maxSpeed * 1000m/3600s * t) / (1.5 * 5 / 对角线)
					if (movedDis <= checkDis)
					{
						float hwRatio = target->getLatestRect().getHWRatio();
						if (hwRatio >= minHWRatio)
						{
							evt->setTarget(target);
							evt->updateOccurRect(target->getLatestRect());
							evt->setState(EventState_Confirming);
							evt->setPadding(eventSpace);
							evt->setStateLife(0.0f);
						}
					}
				}
				else
				{
					evt->setState(EventState_None);
					evt->setStateLife(0.0f);
				}
			}
		}
		else if (evt->getStateLife() < 0.0f)
		{
			evt->setState(EventState_None);
			evt->setStateLife(0.0f);
		}
	}

	/**
	 * 事件维持
	 * @param evt
	 * @param target
	 */
	void PedestrainEventDetector::onEventMaintaining(EventPtr evt, TargetPtr target)
	{
		evt->setPeriodCheckCount(evt->getPeriodCheckCount() + 1);
		if (matchCheck(evt, target))
			evt->setPeriodPassCount(evt->getPeriodPassCount() + 1);

		if (evt->getPeriodCheckCount() > frameRate)
		{
			evt->getPeriodPassCount() > (int)(checkRate * (float)frameRate) ? evt->addStateLife(1.f) : evt->addStateLife(-1.f);
			evt->setPeriodCheckCount(0);
			evt->setPeriodPassCount(0);
		}
		if (evt->getStateLife() < 0.0f)
		{
			evt->setState(EventState_Released);
			evt->setStateLife(0.0f);
		}
		else
		{
			if (evt->getStateLife() > checkTime)
			{
				evt->setStateLife(checkTime);
			}
		}
	}

	/**
	 * 事件解除
	 * @param evt
	 * @param target
	 */
	void PedestrainEventDetector::onEventRelease(EventPtr evt, TargetPtr target)
	{
		evt->setPeriodCheckCount(evt->getPeriodCheckCount() + 1);
		if (matchCheck(evt, target))
			evt->setPeriodPassCount(evt->getPeriodPassCount() + 1);

		if (evt->getPeriodCheckCount() > frameRate)
		{
			bool periodCheckPassed = evt->getPeriodPassCount() > (int)(checkRate * (float)frameRate);
			periodCheckPassed ? evt->addStateLife(-1) : evt->addStateLife(1);
			evt->setPeriodCheckCount(0);
			evt->setPeriodPassCount(0);

			// 如果 区间计数满足 重置解除时间
			if (periodCheckPassed && evt->getStateLife() > 0.0f)
				evt->setStateLife(0.0f);
		}
		if (evt->getStateLife() > removeTime)
		{
			evt->setState(EventState_Removed);
			evt->setStateLife(0.0f);
		}
		else if (evt->getStateLife() + checkTime < 0)
		{
			evt->setState(EventState_Maintaining);
			evt->setStateLife(0.0f);
		}
	}

	/**
	 * 事件目标匹配检查
	 * @param evt
	 * @param target
	 */
	bool PedestrainEventDetector::matchCheck(EventPtr evt, TargetPtr target)
	{
		if (target == NULL)
			return false;

        if (evt->getTargetID() == target->getID()
            || (checkTargetType(target->getType()) && checkTargetClassType(target)))
			return true;
		else
			return false;
	}

	/**
	 * 目标速度过滤检查 满足返回true
	 * @param target
	 */
	bool PedestrainEventDetector::checkSpeed(TargetPtr target, bool ignoreStill)
	{
		if(ignoreStill && target->getAccuVelocity().length() < 5.0f)
			return false;

		float speedRatio = target->getAverageSpeedRatio();
		float speed = speedRatio * frameRate * PEDESTRAIN_SPEED_TRANS_COEFF;

		if (speed <= maxSpeed )
			return true;
		else
			return false;
	}


	/**
	 * 区域配置更新
	 */
	void PedestrainEventDetector::onUpdateRegionConfig()
	{
		regionConfig.getValue<float>(proposeTime, PEDSTRAIN_PROPOSE_TIME);
		regionConfig.getValue<float>(checkTime, PEDSTRAIN_CHECK_TIME);
		regionConfig.getValue<float>(removeTime, PEDSTRAIN_REMOVE_TIME);
		regionConfig.getValue<float>(checkRate, PEDSTRAIN_CHECK_RATE);
		regionConfig.getValue<int>(eventSpace, PEDSTRAIN_EVENT_SPACE);
		regionConfig.getValue<float>(maxSpeed, PEDSTRAIN_MAX_SPEED);
		regionConfig.getValue<float>(minHWRatio, PEDSTRAIN_MIN_HW_RATIO);

		checkFrameCount = (int)(proposeTime * (float)frameRate);
	}
}
