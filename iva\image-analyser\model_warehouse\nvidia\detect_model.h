#pragma once

#include "base_model.h"
#include "opencv2/core/core.hpp"
#include <opencv2/opencv.hpp>
#include <string>
#include <vector>


namespace model_warehouse
{
    using DETECT_RESULT = std::vector<std::vector<ObjBox>>;
    class DetectModel: public BaseModel<DETECT_RESULT>
    {
    public:
        DetectModel(std::string modelPath, int deviceID, float scoreThres, float iouThres, int paddingValue);
        ~DetectModel() override = default;

        using BaseModel::infer;

        /**
         * 模型加载，资源初始化
         */
        void initResource() override;

        /**
         * @brief                  执行一次图像推理流程：预处理、推理、后处理输出
         * @param[in] images       原始输入图像
         * @param[in]  padding     是否进行图像padding
         * @return                 推理后处理输出结果
         */
        DETECT_RESULT infer(std::vector<cv::Mat> images, bool padding) override;

        /**
         * @brief                  推理后处理，处理推理输出结果
         * @param imageSize        推理图像数量
         * @return                 推理后处理输出结果
         */
        DETECT_RESULT postprocess(int imageSize) override;

    private:
        /**
         * @brief                  图像padding处理
         * @param imageSize        待处理的图像
         * @return                 处理完的图像
         */
        std::vector<cv::Mat> imagePadding(std::vector<cv::Mat>& images) override;

        /**
         * @brief 初始化模型输出维度信息
         */
        void initOutputDimensions() override;
    private:
        int m_outputNum;
        int m_classNum;
        int m_boxNum;
        float m_scoreThres;
        float m_iouThres;
        float m_ratioH;
        float m_ratioW;

        std::vector<int> m_hStarts;
        std::vector<int> m_wStarts;
    };
}
