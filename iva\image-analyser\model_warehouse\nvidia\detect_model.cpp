#include "detect_model.h"

#include <utility>

namespace model_warehouse{

    DetectModel::DetectModel(std::string modelPath, int deviceID, float scoreThres, float iouThres, int paddingVal) : BaseModel(modelPath, deviceID)
    {
        m_scoreThres = scoreThres;
        m_iouThres = iouThres;
        paddingValue = paddingVal;
    }

    /**
     * 模型加载，资源初始化
     */
    void DetectModel::initResource()
    {
        if (!modelInited)
        {
            predictor.init(std::move(modelPath), deviceId);

            initInputDimensions();
            initOutputDimensions();

            if (!scoreDataHost)
                scoreDataHost = std::shared_ptr<float[]>(new float[batchSize * m_outputNum * m_classNum]);
            if (!boxDataHost)
                boxDataHost = std::shared_ptr<float[]>(new float[batchSize * m_outputNum * m_boxNum]);

            if (!scoreDataDevice)
                CHECK_CUDA_STATUS(cudaMalloc(&scoreDataDevice, batchSize * m_outputNum * m_classNum * sizeof(float)), "cudaMalloc failed", true);
            if (!boxDataDevice)
                CHECK_CUDA_STATUS(cudaMalloc(&boxDataDevice, batchSize * m_outputNum * m_boxNum * sizeof(float)), "cudaMalloc failed", true);
            if (!imageDataDevice)
                CHECK_CUDA_STATUS(cudaMalloc(&imageDataDevice, batchSize * inputWidth * inputHeight * inputChannel * sizeof(float)), "cudaMalloc failed", true);

            modelInited = true;
        }
    }

    /**
     * @brief 初始化模型输出维度信息
     */
    void DetectModel::initOutputDimensions()
    {
        auto dims1 = predictor.getBindingDimensions(1);
        auto dims2 = predictor.getBindingDimensions(2);
        m_outputNum = dims1.d[1];
        m_classNum  = dims1.d[2];
        m_boxNum    = dims2.d[3];
    }

    /**
     * @brief                  执行一次图像推理流程：预处理、推理、后处理输出
     * @param[in] images       原始输入图像
     * @param[in]  padding     是否进行图像padding
     * @return                 推理后处理输出结果
     */
    DETECT_RESULT DetectModel::infer(std::vector<cv::Mat> images, bool padding)
    {
        vector<float> imageData;
        preprocess(images, padding, true, imageData);

        CHECK_CUDA_STATUS(cudaMemcpy(imageDataDevice, imageData.data(), imageData.size() * sizeof(float), cudaMemcpyHostToDevice),"Failed to copy image data to device", false);
        vector<void *> buffers = {imageDataDevice, scoreDataDevice, boxDataDevice};
        if (!predictor.infer(buffers, (int)images.size()))
            return {};

        return postprocess((int)images.size());
    }

    /**
     * @brief                  推理后处理，处理推理输出结果
     * @param imageSize        推理图像数量
     * @return                 推理后处理输出结果
     */
    DETECT_RESULT DetectModel::postprocess(int imageSize)
    {
        std::vector<std::vector<ObjBox>> outBoxes;
        //Transfer data and score from gpu to cpu
        CHECK_CUDA_STATUS(cudaMemcpy(scoreDataHost.get(), scoreDataDevice, batchSize * m_outputNum * m_classNum * sizeof(float), cudaMemcpyDeviceToHost), "Failed to copy score data to host", false);
        CHECK_CUDA_STATUS(cudaMemcpy(boxDataHost.get(), boxDataDevice, batchSize * m_outputNum * m_boxNum * sizeof(float), cudaMemcpyDeviceToHost), "Failed to copy box data to host", false);

        //save the boxes which's score more than scoreThres
        for(int b=0; b < batchSize; b++)
        {
            std::vector<ObjBox> boxes;
            std::vector<float> scores,outScores;
            int boxLen = 4;
            for(int i=0;i<m_outputNum;i++)
            {
                int index = -1;
                float scoreMax = -1;
                for(int j=0;j<m_classNum;j++)
                {
                    if(scoreMax < scoreDataHost[b * m_outputNum * m_classNum + i * m_classNum + j])
                    {
                        scoreMax = scoreDataHost[b * m_outputNum * m_classNum + i * m_classNum + j];
                        index = j;
                    }
                }
                if(scoreMax>m_scoreThres)
                {
                    ObjBox box;
                    int bStart = b*m_outputNum*m_boxNum;
                    xyxy2objBox((boxDataHost[bStart + boxLen * i] - m_wStarts[b]) / m_ratioW, (boxDataHost[bStart + boxLen * i + 1] - m_hStarts[b]) / m_ratioH, (boxDataHost[bStart + boxLen * i + 2] - m_wStarts[b]) / m_ratioW, (boxDataHost[bStart + boxLen * i + 3] - m_hStarts[b]) / m_ratioH, box);
                    box.score = scoreMax;
                    box.class_ = index;
                    boxes.push_back(box);
                    scores.push_back(scoreMax);
                }
            }
            std::vector<ObjBox> outBox;
            nonMaximumSuppression(boxes, scores, m_iouThres,outBox);
            outBoxes.push_back(outBox);
        }
        return outBoxes;
    }


    /**
     * @brief                  图像padding处理
     * @param imageSize        待处理的图像
     * @return                 处理完的图像
     */
    std::vector<cv::Mat> DetectModel::imagePadding(std::vector<cv::Mat>& images)
    {
        std::vector<cv::Mat> imgPaddings;
        m_hStarts.clear();
        m_wStarts.clear();
        for (int i = 0; i < images.size(); i++)
        {
            cv::Mat img = images[i];
            if (!img.empty())
            {
                //padding img
                int h = img.size().height;
                int w = img.size().width;
                float ratio = min(inputWidth * 1.0 / w, inputHeight * 1.0 / h);
                m_ratioH = ratio;
                m_ratioW = ratio;
                w = int(w * ratio);
                h = int(h * ratio);
                cv::resize(img, img, cv::Size(w, h), (0.0), (0.0), cv::INTER_LINEAR);
                cv::Mat imgPadding = cv::Mat::zeros(inputHeight, inputWidth, CV_8UC3);
                imgPadding.setTo(paddingValue);
                m_wStarts.push_back(int((inputWidth - w) / 2));
                m_hStarts.push_back(((inputHeight - h) / 2));
                cv::Rect roi = cv::Rect(m_wStarts[i], m_hStarts[i], w, h);
                img.copyTo(imgPadding(roi));
                imgPaddings.push_back(imgPadding);
            }
        }
        return imgPaddings;
    }


}
