#pragma once

#include "opencv2/core/core.hpp"
#include <opencv2/opencv.hpp>
#include <string>
#include <vector>

#define USING_DETECTMODEL

namespace roiObjectDetect{

    struct GridCoord
    {
        int x;
        int y;
        int i;
    };

    struct ObjBox
    {
        int x;
        int y;
        int width;
        int height;
        float score;
        int class_;
        std::vector<GridCoord> gridCoords;
    };

    struct FeatureModelParam
    {
        std::string modelDir;
        std::string modelPath;
        int maxFeatureNum;
        int imgInputW;
        int imgInputH;
        int gridW;
        int gridH;
        int downScale;
    };

    struct DetectModelParam
    {
        std::string modelPath;
        float scaleForSeg;
        int inputW;
        int inputH;
        bool segMode;
        float nmsIouThres;
        float scoresThres;
        float rectIouThres;
        int batch;
        int paddingValue;
    };

    struct ClassModelParam
    {
        std::string modelPath;
        float scaleForSeg;
        float scaleForPadding;
        float scoresGip;
        bool segMode;
        int batch;
        int paddingValue;
    };

    struct BgImgGeneratorParam
    {
        float motionThres;
        float bgFrontThres;
        float unFillRatioThres;
    };

    struct DistanceSolverParam
    {
        float updataDisThres;
        int maxDistanceNum;
        int validDistanceNum;
        int moveDistanceNum;
    };

    struct SelectRectParam
    {
        bool isOpening;
        float iouThresForSelect;
        float iouThresForAlarm;
        float scoreThresForBinary;
        int minW;
        int minH;
        int maxW;
        int maxH;
        cv::Mat interestedMask;
        int roadNums;
        float farRatio;
        float carLeastWidth;
        std::vector<float> carNumForRoads;
    };

    struct RoiObjectTrackerParam
    {
        int staticFrontTimesThres;
        int maxDecreaseTimes;
        float compareIouThres1;
        float compareIouThres2;
        bool isUseRectFlag;
        float compareStaticThres;
        bool newTraceAppendFlag;
    };

    struct ImgSavePathParam
    {
        bool saveBinaryImgFlag;
        bool saveBgImgFlag;
        bool saveOtherImgsFlag;
        std::string imgSaveDir;
        std::string bgDir;
        std::string binaryDir;
        std::string interestedDir;
        std::string alarmDir;
        std::string classifyDir;
        std::string detectDir;
        std::string classSegDir;
        std::string detectSegDir;
        std::string checkBgDir;
    };

    struct ROIObjectParam
    {
        //selectParams
        SelectRectParam* selectRectParam;
        RoiObjectTrackerParam* roiObjectTrackerParam;
        // nums of multi distances
        int maxFeatureNum;  
        // orgin img
        cv::Mat orginImg;
        // resize to featureModel's input size
        cv::Mat resizeImg;
        // orgin w/h
        int imgW;  
        int imgH;  
        // featureModel's input size
        int imgFeatureInputW;
        int imgFeatureInputH;
        // downScale of feature model
        int downScale;
        //ratio from orgin_size to input_size
        float ratioW;
        float ratioH;
        int featureBatch;  
        //interested mask
        cv::Mat interestedMask;
        //data after imgPreprocess
        std::vector<float> imageData;
        std::vector<float> bgImageData;
        std::vector<float> newBgImageData;
        //state of every grid , to record the grid is fill or not
        std::vector<bool> bgFillFlags;
        //distances between current img to many previous imgs
        std::vector<std::vector<double>> distances;
        //index of current img'feature
        int cudaScoresIndex;
        //single distance , multi distance
        std::vector<double> singleDis1;
        std::vector<double> singleDis2;
        std::vector<double> singleDis4;
        std::vector<double> singleDis5;
        std::vector<double> multiDis;
        //distance between current img to background img
        std::vector<double> bgFrontDis;
        std::vector<double> towBgDis;
        //flag to record bgImagePreprocess
        bool isHasBgImagePreprocess;
        //background img
        cv::Mat bgImg;
        cv::Mat newBgImg;
        cv::Mat binaryImg;
        std::vector<ObjBox> towBgRects;
        //rects without traces
        std::vector<ObjBox> singleFrameRects;
        std::vector<cv::Mat> singleFrameRois;
        int staticFrontTimesThres;
        int maxDecreaseTimes;
        std::vector<bool> singleFrameRectAlarmFlags;
        //static rects
        std::vector<ObjBox> alarmRects;
        //rects which selecting by interested mask
        std::vector<ObjBox> interestedRects;
        cv::Mat segClassImg; // to classModel;
        //rects which selecting by class model
        std::vector<ObjBox> classifyRects;
        //scoreDiv between score0 and  score1
        float scoreDiv;
        cv::Mat segDetectImg; // to detectModel;
        std::vector<ObjBox> detectRects;

        //newBg is gen
        bool isNewBgGen;
        //newBg update is not finished
        int isNewBgFinished;
        bool isBgChanged;
    };
}