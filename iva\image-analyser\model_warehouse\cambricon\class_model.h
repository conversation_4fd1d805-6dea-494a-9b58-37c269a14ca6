#pragma once

#include "base_model.h"
#include "opencv2/core/core.hpp"
#include <opencv2/opencv.hpp>
#include <string>
#include <utility>
#include <variant>
#include <vector>

namespace model_warehouse
{
    using CLASS_RESULT = std::vector<std::vector<float>>;
    //using PREPROCESS_FUNC = std::function<void(std::vector<cv::Mat>&, int , int , std::vector<float>&)>;
    using PREPROCESS_FUNC = std::function<cv::Mat(cv::Mat srcImg, int dstHeight, int dstWidth, bool keepAspectRatio)>;
    class ClassModel: public BaseModel<CLASS_RESULT>
    {
    public:
        ClassModel(std::string modelPath, int deviceID, bool isVehicle = false, std::string meanFile = "");
        ~ClassModel() override = default;

        /**
         * 模型加载，资源初始化
         */
        void initResource() override;

        /**
         * @brief                  执行一次图像推理流程：预处理、推理、后处理输出
         * @param[in] images       原始输入图像
         * @param[in]  padding     是否进行图像padding
         * @return                 推理后处理输出结果
         */
        CLASS_RESULT infer(std::vector<cv::Mat> images, bool padding) override;

        /**
         * @brief                  推理后处理，处理推理输出结果
         * @param[in] imageSize    推理图像数量
         * @return                 推理后处理输出结果
         */
        CLASS_RESULT postprocess(std::vector<ModelOutput>& inferOutputs) override;


        std::optional<int> getMaxResult(std::vector<float>& results, float threshold);

        inline void setPreprocessFunc(PREPROCESS_FUNC func,bool keepAspectRatio = true) { preprocessFunc = std::move(func); isKeepAspectRatio = keepAspectRatio;}
        inline void setNetworkInputType(NetworkInputType type) { networkInputType = type; }

    private:
        /**
         * @brief 初始化模型输入维度信息
         */
        void initInputDimensions() override;
        /**
         * @brief 初始化模型输出维度信息
         */
        void initOutputDimensions() override;

    private:
        bool isVehicleModel = false;
        bool isKeepAspectRatio = true;
        PREPROCESS_FUNC preprocessFunc = nullptr;
    };
}