#include "pipeline_factory.h"
#include "ivaconfig.hpp"
#include "version.h"

namespace iva::pipeline
{
	/**
	 * @brief decodebin pad-added信号回调，将decodebin的srcpad和下个原件的sinkpad连接起来
	 * @note  decodebin根据原始数据格式创建src pad后，触发回调。
	 */
	static void onDecodeBinNewPad(GstElement* decode_bin, GstPad* pad, gpointer data);

	/**
	 * @brief decodebin child-added信号回调，设置动态创建好的子元素的相关属性
	 * @note  decodebin根据原始数据格式动态创建相关子元素后触发该信号。
	 */
	static void onDecodeBinChildAdded(GstChildProxy* child_proxy, GObject* object, gchar* name, gpointer user_data);

	/**
	 * @brief 创建单个通道的pipeline的source bin (取流、rtp解包、解码、裁剪 等插件配置)
	 * @param[in] 通道序号(从0开始)
	 */
	GstElement* createSourceBin(guint index)
	{
		gchar elem_name[50];

		// src bin
		GST_ELEMENT_INDEX_NAME(elem_name, "src_bin", index);
		auto src_bin = gst_bin_new(elem_name);
		GST_ELEMENT_CHECK(src_bin, elem_name);
		ElementBin elementBin(src_bin);

		// updsrc
		GST_ELEMENT_INDEX_NAME(elem_name, "udp_src", index);
		auto udp_src = gst_element_factory_make("udpsrc", elem_name);
		GST_ELEMENT_CHECK(udp_src, elem_name);
		//default:0.0.0.0
		g_object_set(G_OBJECT(udp_src), "address", "127.0.0.1", NULL);
		//default 50K~100K auto,it corresponds to the sending parameter of fvm(1024000)
		int udp_buffer_size = SETTINGS->udpBufferSize();
		if (udp_buffer_size > 0)
			g_object_set(G_OBJECT(udp_src), "buffer-size", udp_buffer_size, NULL);
		elementBin.linkNext(udp_src);

		// queue for udp，udp speed is very fast, so thread is recommented
		GST_ELEMENT_INDEX_NAME(elem_name, "src_udp_queue", index);
		auto udp_queue = gst_element_factory_make("queue", elem_name);
		GST_ELEMENT_CHECK(udp_queue, elem_name);
		elementBin.linkNext(udp_queue);

		// capsfilter
		GST_ELEMENT_INDEX_NAME(elem_name, "rtpdepay_capsfilter", index);
		auto rtpdepay_capsfilter = gst_element_factory_make("capsfilter", elem_name);
		GST_ELEMENT_CHECK(rtpdepay_capsfilter, elem_name);
		auto enc_caps = gst_caps_new_simple("application/x-rtp",
			"media", G_TYPE_STRING, "video",
			"encoding-name", G_TYPE_STRING, "H264",
			"payload", G_TYPE_INT, 96,
			"clock-rate", G_TYPE_INT, 90000,
			NULL);
		g_object_set(G_OBJECT(rtpdepay_capsfilter), "caps", enc_caps, NULL);
		elementBin.linkNext(rtpdepay_capsfilter);

		// rtph264depay
		GST_ELEMENT_INDEX_NAME(elem_name, "rtph264_depay", index);
		auto rtph264_depay = gst_element_factory_make("rtph264depay", elem_name);
		GST_ELEMENT_CHECK(rtph264_depay, elem_name);
		elementBin.linkNext(rtph264_depay);

#if defined(USE_VIDEO_RECORD) || defined(IVA_EVENT_VIDEO_RECORD)
		GST_ELEMENT_INDEX_NAME(elem_name, "tee", index);
		auto tee = gst_element_factory_make("tee", elem_name);
		GST_ELEMENT_CHECK(tee, elem_name);
		elementBin.linkNext(tee);

		if (SETTINGS->recordVideo())
			linkRecordBin(index, src_bin, tee);
#endif

#ifndef NVIDIA
		/// h264Parse
		GST_ELEMENT_INDEX_NAME(elem_name, "h264parse", index);
		auto h264parse = gst_element_factory_make("h264parse", elem_name);
        g_object_set(G_OBJECT(h264parse), "disable-passthrough", true, NULL);
        g_object_set(G_OBJECT(h264parse), "config-interval", 1, NULL);
		GST_ELEMENT_CHECK(h264parse, elem_name);
		elementBin.linkNext(h264parse);
#endif

#ifdef HUAWEI
		// stabel queue
		GST_ELEMENT_INDEX_NAME(elem_name, "stablequeue", index);
		auto stablequeue = gst_element_factory_make("stablequeue", elem_name);
		GST_ELEMENT_CHECK(stablequeue, elem_name);
		elementBin.linkNext(stablequeue);

#endif

		// decodebin
		GST_ELEMENT_INDEX_NAME(elem_name, "decodebin", index);
		auto decodebin = gst_element_factory_make(AI_DECODER, elem_name);
		GST_ELEMENT_CHECK(decodebin, elem_name);
#ifndef NVIDIA
		g_object_set(G_OBJECT(decodebin), "device-id", deviceId, NULL);
#endif
		elementBin.linkNext(decodebin);
#ifdef HUAWEI
		{
			g_object_set(G_OBJECT(decodebin), "input-buffer-num", 10, NULL);
			g_object_set(G_OBJECT(decodebin), "output-buffer-num", 15, NULL);
			g_object_set(G_OBJECT(decodebin), "process-id", processId, NULL);
			g_object_set(G_OBJECT(decodebin), "max-decode-channels", 50, NULL);	//only for 310p:256, one device for one process

			gchar buf[128] = { 0 };
			g_snprintf(buf, sizeof(buf), "idx:%02d,dev:%d", index, deviceId);
			g_object_set(G_OBJECT(decodebin), "debug-info", buf, NULL);
		}
#endif

#ifdef NVIDIA
		// queue after decode
		GST_ELEMENT_INDEX_NAME(elem_name, "src_dec_after_queue", index);
		auto dec_after_queue = gst_element_factory_make("queue", elem_name);
		GST_ELEMENT_CHECK(dec_after_queue, elem_name);
		//g_object_set(G_OBJECT(dec_after_queue), "leaky", 2, NULL);
		//g_object_set(G_OBJECT(dec_after_queue), "flush-on-eos", true, NULL);
		g_object_set(G_OBJECT(dec_after_queue), "max-size-buffers", SETTINGS->maxSizeBuffers(), NULL);
		g_object_set(G_OBJECT(dec_after_queue), "max-size-bytes", 0, NULL);
        elementBin.add(dec_after_queue);
#endif // NVIDIA

#ifndef HUAWEI
		// crop converter
		g_snprintf(elem_name, sizeof(elem_name), "crop_conv%d", index);
		auto crop_conv = gst_element_factory_make(AI_CONVERTER, elem_name);
		GST_ELEMENT_CHECK(crop_conv, elem_name);
		GstElement* last_elem = crop_conv;
		elementBin.linkNext(crop_conv);
    #ifdef CAMBRICON
        g_object_set(G_OBJECT(crop_conv), "npu-id", deviceId, NULL);
    #endif
#endif // HUAWEI

#ifndef NVIDIA
		// capsfilter
		g_snprintf(elem_name, sizeof(elem_name), "crop_capsfilter%d", index);
		GstElement* crop_capsfilter = gst_element_factory_make("capsfilter", elem_name);
		GST_ELEMENT_CHECK(crop_capsfilter, elem_name);

		gchar caps_str[128];
#ifdef CAMBRICON_MLU370
		g_snprintf(caps_str, sizeof(caps_str), "video/x-raw(memory:mlu), width=%d, height=%d, format=RGB", SETTINGS->videoWidth(), SETTINGS->videoHeight());
#elif CAMBRICON_MLU270
        g_snprintf(caps_str, sizeof(caps_str), "video/x-raw(memory:mlu), width=%d, height=%d, format=RGB", 640, 640); ///< TODO 格式、尺寸从推理插件获取
#elif CAMBRICON_MLU220
        g_snprintf(caps_str, sizeof(caps_str), "video/x-raw(memory:mlu), width=%d, height=%d, format=RGBA", SETTINGS->videoWidth(), SETTINGS->videoHeight());
#elif HUAWEI
		g_snprintf(caps_str, sizeof(caps_str), "video/x-raw(memory:mlu), width=%d, height=%d, format=NV12", SETTINGS->videoWidth(), SETTINGS->videoHeight());
#endif
		GstCaps* crop_caps = gst_caps_from_string(caps_str);
		g_object_set(G_OBJECT(crop_capsfilter), "caps", crop_caps, NULL);
		elementBin.linkNext(crop_capsfilter);
		//last_elem = crop_capsfilter;
#else
		g_object_set(G_OBJECT(crop_conv), "gpu-id", deviceId, NULL);
#endif // !NVIDIA


#ifdef NVIDIA
	#ifdef USE_NEW_STREAMMUX
		// capsfilter
		g_snprintf(elem_name, sizeof(elem_name), "crop_capsfilter%d", index);
		GstElement* crop_capsfilter = gst_element_factory_make("capsfilter", elem_name);
		GST_ELEMENT_CHECK(crop_capsfilter, elem_name);

		gchar caps_str[128];
		g_snprintf(caps_str, sizeof(caps_str), "video/x-raw(memory:NVMM), width=%d, height=%d, format=NV12", SETTINGS->videoWidth(), SETTINGS->videoHeight());
		GstCaps* crop_caps = gst_caps_from_string(caps_str);
		g_object_set(G_OBJECT(crop_capsfilter), "caps", crop_caps, NULL);
		last_elem = crop_capsfilter;
        elementBin.linkNext(crop_capsfilter);
	#endif // !USE_NEW_STREAMMUX

		g_signal_connect(G_OBJECT(decodebin), "pad-added", G_CALLBACK(onDecodeBinNewPad), dec_after_queue);
		g_signal_connect(G_OBJECT(decodebin), "child-added", G_CALLBACK(onDecodeBinChildAdded), dec_after_queue);
#endif // NVIDIA

		NVGSTDS_BIN_ADD_GHOST_PAD(src_bin, elementBin.last, "src");
		//NVGSTDS_BIN_ADD_GHOST_PAD(src_bin, last_elem, "src");
		return src_bin;
	}

	static void onDecodeBinNewPad(GstElement* decode_bin, GstPad* pad, gpointer data)
	{
		GstCaps* caps = gst_pad_query_caps(pad, NULL);
		const GstStructure* str = gst_caps_get_structure(caps, 0);
		const gchar* name = gst_structure_get_name(str);

		/* Need to check if the pad created by the decodebin is for video and not audio. */
		if (!strncmp(name, "video", 5))
		{
			GstElement* sinkelement = (GstElement*)data;
			GstPad* sinkpad = gst_element_get_static_pad(sinkelement, "sink");
			if (gst_pad_link(pad, sinkpad) != GST_PAD_LINK_OK)
			{
				NVGSTDS_ERR_MSG_V("Failed to link decodebin to pipeline");
			}
			gst_object_unref(sinkpad);
		}
		gst_caps_unref(caps);
	}

	static void onDecodeBinChildAdded(GstChildProxy* child_proxy, GObject* object, gchar* name, gpointer user_data)
	{
		if (g_strrstr(name, "decodebin") == name)
		{
			g_signal_connect(G_OBJECT(object), "child-added", G_CALLBACK(onDecodeBinChildAdded), user_data);
		}
		if ((g_strrstr(name, "h264parse") == name) || (g_strrstr(name, "h265parse") == name))
		{
			g_object_set(object, "config-interval", 1, NULL);
		}
		if (g_strrstr(name, "fakesink") == name)
		{
			g_object_set(object, "enable-last-sample", FALSE, NULL);
		}
		if (g_strrstr(name, "nvcuvid") == name)
		{
			g_object_set(object, "gpu-id", deviceId, NULL);
			g_object_set(G_OBJECT(object), "cuda-memory-type", 0, NULL);
		}
		if (g_strstr_len(name, -1, "nvv4l2decoder") == name)
		{
			//if (config->Intra_decode)
			//	g_object_set(object, "skip-frames", 2, NULL);
#ifdef __aarch64__
			g_object_set(object, "enable-max-performance", TRUE, NULL);
#else
			g_object_set(object, "gpu-id", deviceId, NULL);
			g_object_set(G_OBJECT(object), "cudadec-memtype", 0, NULL);
#endif
			g_object_set(object, "drop-frame-interval", 0, NULL);
			g_object_set(object, "num-extra-surfaces", 0, NULL);
			//g_object_set(object, "low-latency-mode", SETTINGS->lowLatencyMode(), NULL);
		}
	}
}
