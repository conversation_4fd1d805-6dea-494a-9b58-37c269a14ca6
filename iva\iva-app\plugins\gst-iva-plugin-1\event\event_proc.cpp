#include <future>
#include <experimental/filesystem>
#include "event_proc.h"
#include "ivautils.h"
#include "ivaconfig.hpp"
#include "log.h"
#include "protocol_manager.h"
#include "protocol_sender.h"
#include "protocol_utility.h"
#include "opencv_frame_osd.h"
#include "opencv2/imgcodecs.hpp"
#ifdef NVIDIA
#include "event_video_record.h"
#endif

#if defined(CAMBRICON_MLU370) or defined(NVIDIA)
#include "pedestrian_classify_task.h"
#include "vehiclecolor_classify_task.h"
#include "vehicletype_classify_task.h"
#include "frame_data.h"
#endif

using namespace iva;
using namespace std;
using namespace network;

static auto BOX_RED_COLOR  = cv::Scalar(0, 0, 255);
static auto BOX_CRAY_COLOR = cv::Scalar(255, 255, 0);
const std::string SNAP_IMG_PATH = "/data/opt/tomcat/webapps/snapimg";

void saveImgTask( int channelId, cv::Mat img, std::vector<Area_t> eventAreas,
                  Area_t multiArea, const std::string& imgPath, const std::string& eventName)
{
    ai::CvFrameOsd cvFrameOsd;
    if (SETTINGS->usingCustomOsd())
    {
        auto fontPath = SETTINGS->fontPath();
        cvFrameOsd.init(fontPath);
    }

	int thickness = img.rows > 1000 ? 2 : 1;

#ifdef CAMBRICON
    thickness = 2;
#endif

	int jpegQuality = img.rows > 1000 ? 70 : 90;

    //下面是报事件的目标矩形框，可能有多个
    bool bDrawObj = false;
    auto sz = multiArea.vtPoint.size();
    if (sz > 0 && sz % 2 == 0)
    {
        int subNum = (int)sz / 2;
        for (int i = 0; i < subNum; i++)
        {
            int leftX = (int)multiArea.vtPoint[i * 2].x;
            int leftY = (int)multiArea.vtPoint[i * 2].y;
            int boxWidth = (int)multiArea.vtPoint[i * 2 + 1].x - leftX;
            int boxHeight = (int)multiArea.vtPoint[i * 2 + 1].y - leftY;

            cv::Rect rt;
            const int padding = 3;
            rt.x = std::max(int(leftX - padding), 0);
            rt.y = std::max(int(leftY - padding), 0);
            rt.width = std::min(int((boxWidth + 2 * padding)), img.cols - rt.x);
            rt.height = std::min(int((boxHeight + 2 * padding)), img.rows - rt.y);

            if (SETTINGS->usingCustomOsd())
            {
                cvFrameOsd.drawTargets(img, rt, eventName);
            }
            else
            {
                cv::rectangle(img, rt, BOX_RED_COLOR, thickness);
            }

        }
        bDrawObj = true;
    }
    //如果已经画了目标框，下面就是画所在的范围，用普通颜色，否则表示拥堵，要画红色
    //cv::Scalar polyColor = (bDrawObj) ? BOX_CRAY_COLOR : BOX_RED_COLOR;
    cv::Scalar polyColor = BOX_RED_COLOR;
    //画每个区域的多边形
    for (const auto& eventArea : eventAreas)
    {
        sz = eventArea.vtPoint.size();
        if (sz > 0)
        {
            std::vector<cv::Point> vtPoint;
            for (size_t i = 0; i < sz; i++)
            {
                cv::Point pt((int)eventArea.vtPoint[i].x, (int)eventArea.vtPoint[i].y);
                vtPoint.push_back(pt);
            }
            // 闭合多边形
            vtPoint.push_back(vtPoint[0]);

            // 画线
            for (size_t i = 0; i < vtPoint.size() - 1; i++)
            {
                cv::Point p1(vtPoint[i].x, vtPoint[i].y);
                cv::Point p2(vtPoint[i + 1].x, vtPoint[i + 1].y);
                cv::line(img, p1, p2, polyColor, thickness, cv::LINE_AA);
            }
        }
    }

    try {
		cv::imwrite(imgPath, img, { cv::IMWRITE_JPEG_QUALITY, jpegQuality });
    }
    catch (const cv::Exception& e)
    {
        cout << "[plugin1] Channel:"<< channelId << "eventimg save error: " << e.what();
    }
}

void saveEventImg(cv::Mat mat, std::vector<Area_t> eventAreas,  Area_t multiArea,
                  int channelId, const std::string& imgPath, const std::string& eventName)
{
    auto future = std::async(std::launch::async,[=](){
        saveImgTask(channelId, mat, eventAreas, multiArea, imgPath, eventName);
    });
}

void snapImg(const cv::Mat& mat, int videoId, int index, int presetId, float scale_x, float scale_y)
{
    if (mat.empty())
    {
        IVA_LOG_ERROR("Error: Input cv::Mat is empty." )
        return;
    }
    if (scale_x == 0 || scale_y == 0)
    {
        IVA_LOG_ERROR( "Error: scale_x or scale_y cannot be zero." )
        return;
    }

    try {
        auto evtChannel = evt::getChannel(index);
        if (!evtChannel)
        {
            IVA_LOG_ERROR( "Error: Failed to get event channel {}. The channel has not been created.", index)
            return;
        }

        auto roiList = evtChannel->getRoiList(presetId);
        auto imagePath = SNAP_IMG_PATH + "/" + std::to_string(videoId) + ".jpg";
        auto channelId = index + 1;
        
        // 使用多个Area_t组成的数组，每个Area_t表示一个独立的ROI区域
        std::vector<Area_t> roiAreas;

        std::experimental::filesystem::path path(SNAP_IMG_PATH);
        if (!std::experimental::filesystem::exists(path))
        {
            if (!std::experimental::filesystem::create_directories(path))
            {
                IVA_LOG_ERROR("Error: Failed to create directory: {}", SNAP_IMG_PATH)
                return;
            }
        }

        for (auto& roi : roiList)
        {
            // 为每个ROI创建一个独立的Area_t
            Area_t roiArea;
            int areaPointsNum = roi.pointCount();
            for (int k = 0; k < areaPointsNum; ++k)
            {
                auto roiPoint = roi.getPoint(k);
                Point pt(roiPoint.x / scale_x, roiPoint.y / scale_y);
                roiArea.vtPoint.emplace_back(pt);
            }
            // 将完整的ROI区域添加到数组中
            if (!roiArea.vtPoint.empty()) {
                roiAreas.push_back(roiArea);
            }
        }
        saveEventImg(mat, roiAreas, {}, channelId, imagePath, "");
    }
    catch (const std::exception& e)
    {
        IVA_LOG_ERROR( "Error: Failed to save event image: {}", e.what())
    }
}

#if defined(CAMBRICON_MLU370) or defined(NVIDIA)
int getClassificationResult(model_warehouse::CLASS_RESULT inferOutputs, float threshold)
{
    if (inferOutputs.empty())
    {
        IVA_LOG_ERROR("pedestrian classification result is empty!")
        return -1;
    }

    for (auto &imageClass: inferOutputs)
    {
        auto max = model_warehouse::getMaxKlass(imageClass, threshold);
        if (!max.has_value())
            continue;

        const int maxIndex = max.value();
        if (static_cast<size_t>(maxIndex) >= imageClass.size())
        {
            IVA_LOG_ERROR("行人分类结果 {} 超出标签范围 {}", max.value(), imageClass.size());
            continue;
        }
        return maxIndex;
    }
    return -2;
}

template<typename ClassifyMgrType>
void processClassification(int channelId, evt::EventInfo eventInfo, cv::Mat& frame, float scaleX, float scaleY, int deviceId,
                          const std::string& videoPath, const std::string& imgPath, const Area_t &objectArea)
{
    const auto& rect = eventInfo.occurRect;
    const int x = std::clamp(static_cast<int>(rect.x / scaleX), 0, frame.cols - 1);
    const int y = std::clamp(static_cast<int>(rect.y / scaleY), 0, frame.rows - 1);
    const int w = std::min(static_cast<int>(rect.width / scaleX), frame.cols - x);
    const int h = std::min(static_cast<int>(rect.height / scaleY), frame.rows - y);

    cv::Mat roiImg = frame(cv::Rect(x, y, w, h));
    if (roiImg.empty())
        return;

    auto classifyMgr = ClassifyMgrType::getInstance(deviceId, 4);
    if (!classifyMgr)
    {
        IVA_LOG_ERROR("Failed to get classification manager.");
        return;
    }

    const auto processCallback = [=](const auto& inferOut) mutable{
        if (classifyMgr)
        {
            int classId = getClassificationResult(inferOut, classifyMgr->getThreshold());
            auto classifierClassTypes = classifyMgr->getClassifierClassTypes(classId);
            eventInfo.targetClassTypes.emplace(classifierClassTypes);
        }
        protocol::postEvent(channelId, videoPath, imgPath, network::ALARM, objectArea, eventInfo);
    };

    classifyMgr->setOutputCallback(channelId, processCallback);
    classifyMgr->submit(roiImg, nullptr, channelId);
}

// 处理车辆类型分类
void processVehicleTypeClassification(int channelId, evt::EventInfo eventInfo, cv::Mat& frame, float scaleX, float scaleY, int deviceId,
                                      const std::string& videoPath, const std::string& imgPath, const Area_t &objectArea)
{
    processClassification<ia::VehicleTypeClassifyMgr>(channelId, eventInfo, frame, scaleX, scaleY, deviceId, videoPath, imgPath, objectArea);
}

// 处理车辆颜色分类
void processVehicleColorClassification(int channelId, evt::EventInfo eventInfo, cv::Mat& frame, float scaleX, float scaleY, int deviceId,
                                    const std::string& videoPath, const std::string& imgPath, const Area_t &objectArea)
{
    processClassification<ia::VehicleColorClassifyMgr>(channelId, eventInfo, frame, scaleX, scaleY, deviceId, videoPath, imgPath, objectArea);
}

// 处理行人衣物检测
void processPersonClothesClassification(int channelID, evt::EventInfo eventInfo, cv::Mat& frame, float scaleX, float scaleY, int deviceId,
                                    const std::string& videoPath, const std::string& imgPath, const Area_t &objectArea)
{
    processClassification<ia::PedestrianClassifyMgr>(channelID, eventInfo, frame, scaleX, scaleY, deviceId, videoPath, imgPath, objectArea);
}
#endif

/**
 *  更新事件 截图和上传
 */
void updateEvents(int channelID, std::vector<evt::EventInfo>* events, cv::Mat mat, int framenum, float scale_x, float scale_y, guint gpu_id)
{
    if (!events || events->empty())
		return;

	std::string eventImgPath = "eventimg";
	std::string eventVideoPath = "eventvideo";

	//创建当天的图片和视频路径
	std::string szDate = get_system_short_time_str(get_system_timestamp());
	std::string szPath = std::string(WEB_SERVER_ROOT) + "/" + eventImgPath + "/" + szDate + "/";
    protocol::createPath(szPath);
	szPath = std::string(WEB_SERVER_ROOT) + "/" + eventVideoPath + "/" + szDate + "/";
    protocol::createPath(szPath);

	for (guint i = 0; i < events->size(); ++i)
	{
		bool needClone = events->size() > 1 && i < events->size() -1;

		evt::EventInfo& evt = events->at(i);
		std::string evtNum = (events->size() > 1) ? ("_" + std::to_string(i + 1)) : "";

        stringstream fileName;
		if (evt.removeTime > 0)
		{
			std::string imagePath;
			if (evt.type == evt::EventType_Jam || evt.type == evt::EventType_RoadConstruction ) //拥堵解除时要存图片
			{
				//! 事件区域
                Area_t multiArea;
                Area_t eventArea;
				int eventAreaPointsNum = evt.occurArea.pointCount();
				for (int k = 0; k < eventAreaPointsNum; ++k)
				{
                    Point pt(evt.occurArea.getPoint(k).x / scale_x, evt.occurArea.getPoint(k).y / scale_y);
					eventArea.vtPoint.emplace_back(std::move(pt));
				}

				//! 画框存图
                fileName << szDate << "/" << get_system_simple_time_str(evt.removeTime) << "_" << channelID + 1 << "_" << evt.type << "_" << framenum % 1000 << evtNum;
				auto eventName = getEventTypeName(evt.type);
                imagePath = eventImgPath + "/" + fileName.str() + ".jpg";
                std::string szFullPath = std::string(WEB_SERVER_ROOT) + "/" + imagePath;
                saveEventImg(needClone ? mat.clone(): mat, {eventArea}, multiArea, channelID, szFullPath, eventName);
			}
#ifdef NVIDIA
            if (evt.type == evt::EventType_Jam)
            {
                auto eventVideoRecord = record::getEventVideoRecord(channelID);
                if ((eventVideoRecord) && (eventVideoRecord->getRecordType() == record::EvtRecordType::IVA))
                {
                    std::string videoPath = eventVideoPath + "/" + fileName.str() + ".mp4";
                    record::EvtVideoRecordTask task;
                    task.startTime = std::chrono::system_clock::now();
                    task.channelId = channelID;
                    task.videoPath = videoPath;
                    task.imgPath = imagePath;
                    task.eventInfo = evt;
                    eventVideoRecord->submit(task);
                }
				else
				{
					protocol::postEventRemove(evt.id, get_system_full_time_str(evt.removeTime), evt.type, imagePath);
				}
            }
            else
#endif
            {
                protocol::postEventRemove(evt.id, get_system_full_time_str(evt.removeTime), evt.type, imagePath);
            }

			IVA_LOG_INFO("[plugin1]remove event {}/{} channel {}, event id {}, event type {} roi {} lane {} region {}",
				i + 1, events->size(), channelID, evt.id, evt.type, evt.roiID, evt.laneID, evt.regionID);
		}
		else
		{
            //! 事件区域
			bool bDrawObj = false;//有目标框,画目标框，后期可能会是多个目标框，待扩展 todo
            Area_t multiArea, drawMultiArea;
			if ( evt.occurRect.isValid() )
			{
				auto& occurRect = evt.occurRect;
				int occurAreaPointsNum = 2;
				multiArea.vtPoint.emplace_back(occurRect.getLeft() / scale_x, occurRect.getTop() / scale_y);
				multiArea.vtPoint.emplace_back(occurRect.getRight() / scale_x, occurRect.getBottom() / scale_y);

				bDrawObj = true;
			}

			drawMultiArea.vtPoint = multiArea.vtPoint;
            if ((evt.type == evt::EventType_RoadConstruction && SETTINGS->drawConstruct())
                || (evt.type == evt::EventType_Pedstrain && SETTINGS->drawAllPedstrain())
                || (evt.type == evt::EventType_Obstacle)
                || (evt.type == evt::EventType_RoadBlock)
                || (evt.type == evt::EventType_FireSmoke)
                || (evt.type == evt::EventType_Landslide)
                )
            {
                for (auto& rt : evt.extraRects)
                {
					drawMultiArea.vtPoint.emplace_back(rt.getLeft() / scale_x, rt.getTop() / scale_y);
					drawMultiArea.vtPoint.emplace_back(rt.getRight() / scale_x, rt.getBottom() / scale_y);
                }
            }

            Area_t eventArea;	//若没有目标框，或者配置中要画ROI，则画区域
			if (!bDrawObj || SETTINGS->drawEventROI())
			{
				int eventAreaPointsNum = evt.occurArea.pointCount();
				for (int k = 0; k < eventAreaPointsNum; ++k)
				{
                    Point pt(evt.occurArea.getPoint(k).x / scale_x, evt.occurArea.getPoint(k).y / scale_y);
					eventArea.vtPoint.emplace_back(std::move(pt));
				}
			}

            //! 画框存图
            fileName << szDate << "/" << get_system_simple_time_str(evt.occurTime) << "_" << channelID + 1 << "_" << evt.type << "_" << framenum % 1000 << evtNum;
            std::string imagePath = eventImgPath + "/" + fileName.str() + ".jpg";
            std::string videoPath = eventVideoPath + "/" + fileName.str() + ".mp4";
			std::string szFullPath = std::string(WEB_SERVER_ROOT) + "/" + imagePath;
			auto eventName = getEventTypeName(evt.type);
		    // 逆行额外存一张roi框
            if (evt.type == evt::EventType_Opposite)
            {
                Area_t roiArea;
                for (int k = 0; k < evt.roiArea.pointCount(); ++k)
                {
                    Point pt(evt.roiArea.getPoint(k).x / scale_x, evt.roiArea.getPoint(k).y / scale_y);
                    roiArea.vtPoint.emplace_back(std::move(pt));
                }
                auto image = eventImgPath + "/" + fileName.str() + "_check.jpg";
                auto fullPath = std::string(WEB_SERVER_ROOT) + "/" + image;
                saveEventImg(mat.clone(), {roiArea}, {}, channelID, fullPath, eventName);
            }
            saveEventImg(needClone ? mat.clone(): mat, {eventArea}, drawMultiArea, channelID, szFullPath, eventName);
#ifdef NVIDIA
            auto eventVideoRecord = record::getEventVideoRecord(channelID);
            if ((eventVideoRecord) && (eventVideoRecord->getRecordType() == record::EvtRecordType::IVA))
            {
                record::EvtVideoRecordTask task;
                task.startTime = std::chrono::system_clock::now();
                task.channelId = channelID;
                task.videoPath = videoPath;
                task.imgPath = imagePath;
                task.alarmType = network::ALARM;
                task.objectArea = multiArea;
                task.eventInfo = evt;
                eventVideoRecord->submit(task);
            }
            else
#endif
            {
                bool detectType = false;
                bool detectColor = false;
                bool detectClothes = false;
                switch (evt.type)
                {
                    case evt::EventType_Stop:
                        detectType = SETTINGS->detectVehicleTypeOnStop();
                        detectColor = SETTINGS->detectVehicleColorOnStop();
                        if (detectType)
                            processVehicleTypeClassification(channelID, evt, mat, scale_x, scale_y, gpu_id, videoPath, imagePath, multiArea);
                        if (detectColor)
                            processVehicleColorClassification(channelID, evt, mat, scale_x, scale_y, gpu_id, videoPath, imagePath, multiArea);

                        if (!detectType && !detectColor)
                            protocol::postEvent(channelID, videoPath, imagePath, network::ALARM, multiArea, evt);
                        break;
                    case evt::EventType_Opposite:
                        detectType = SETTINGS->detectVehicleTypeOnOpposite();
                        detectColor = SETTINGS->detectVehicleColorOnOpposite();
                        if (detectType)
                            processVehicleTypeClassification(channelID, evt, mat, scale_x, scale_y, gpu_id, videoPath, imagePath, multiArea);
                        if (detectColor)
                            processVehicleColorClassification(channelID, evt, mat, scale_x, scale_y, gpu_id, videoPath, imagePath, multiArea);

                        if (!detectType && !detectColor)
                            protocol::postEvent(channelID, videoPath, imagePath, network::ALARM, multiArea, evt);
                        break;
                    case evt::EventType_Pedstrain:
                        detectClothes = SETTINGS->detectPersonClothesOnPedstrain();
                        if (detectClothes)
                            processPersonClothesClassification(channelID, evt, mat, scale_x, scale_y, gpu_id, videoPath, imagePath, multiArea);
                        else
                            protocol::postEvent(channelID, videoPath, imagePath, network::ALARM, multiArea, evt);
                        break;
                    default:
                        protocol::postEvent(channelID, videoPath, imagePath, network::ALARM, multiArea, evt);
                        break;
                }
            }

			IVA_LOG_INFO("[plugin1]new event {}/{} channel {}, event id {}, event type {}, {}, roi {} lane {} region {}",
				i+1, events->size(), channelID, evt.id, evt.type, getEventTypeName(evt.type), evt.roiID, evt.laneID, evt.regionID);
		}
	}
}

/**
 * 获取事件类型的中文名字
 * @param[in] type 事件类型枚举
 * @return 	  事件类型的中文名字
 */
std::string getEventTypeName(evt::EventType type)
{
	switch (type)
	{
	case evt::EventType_None:
		return "";
	case evt::EventType_Stop:
		return "停车";
	case evt::EventType_Opposite:
		return "逆行";
	case evt::EventType_Jam:
		return "拥堵";
	case evt::EventType_DriveIn:
		return "驶入";
	case evt::EventType_DriveAcross:
		return "变道";
	case evt::EventType_DriveAway:
		return "驶离";
	case evt::EventType_Pedstrain:
		return "行人";
	case evt::EventType_TwoWheels:
		return "摩托车";
	case evt::EventType_Obstacle:
		return "抛洒物";
	case evt::EventType_RoadBlock:
		return "路障";
	case evt::EventType_RoadConstruction:
		return "施工";
	case evt::EventType_FireSmoke:
		return "烟火";
    case evt::EventType_Landslide:
        return "塌方";
    case evt::EventType_Weather_Fog:
        return "大雾";
    case evt::EventType_Weather_Rain:
        return "大雨";
    case evt::EventType_Weather_Snow:
        return "大雪";
	default:
		return "";
	}
}

/**
 *  目标过线回调
 */
void onTargetPassedCallback(evt::TargetInfoList& targets)
{
	//IVA_LOG_INFO("[plugin2]onTargetPassedCallback: target size {} ", targets.size() );
	std::vector<Trackinfo> vtTrack;
	for (auto& t : targets)
	{
        Trackinfo track;
		track.trackId = t.id;
		track.roiId = t.roiID;
		track.laneId = t.laneID;
		track.mainLabel = t.type;
		track.trackSpeed = (float)t.speed;
		track.timeBegin = get_system_full_time_str(t.createTime);
		track.timeEnd = get_system_full_time_str(t.leaveTime);
		vtTrack.emplace_back(std::move(track));
	}
    protocol::postTrack(vtTrack);
}

/**
 *  车辆目标上报回调
 */
void onVehiclesInfoCallback(const std::vector<evt::TargetInfo>& targets)
{
    std::map<int, std::vector<evt::TargetInfo>> roiTargets;
    for (auto& t : targets)
    {
        if (t.type == evt::TargetType_Car || t.type == evt::TargetType_Bus || t.type == evt::TargetType_Truck)
            roiTargets[t.roiID].emplace_back(t);
    }

    std::vector<network::VehicleInfo> vehicleInfos;
    for (auto [roi, targets] : roiTargets)
    {
        network::VehicleInfo vehicleInfo;
        vehicleInfo.roiId = roi;
        vehicleInfo.detectionTime = get_system_time_str_with_ms();
        //std::cout << "roi " << roi << " targets size " << targets.size() << " " << vehicleInfo.detectionTime <<   std::endl;
        for (auto target:targets)
        {
            const auto latestRect = target.tracks.back().rect;
            ObjectRect object = {target.id, latestRect.x / EVT_PROCESS_WIDTH, latestRect.y / EVT_PROCESS_HEIGHT, latestRect.width / EVT_PROCESS_WIDTH, latestRect.height / EVT_PROCESS_HEIGHT};
            vehicleInfo.ObjectRects.emplace_back(object);
        }

        vehicleInfos.emplace_back(vehicleInfo);
    }

    protocol::postVehicleInfo(vehicleInfos);
}

void onEventWithdrawCallback(evt::EventInfo ei)
{
	std::string szOccur = get_system_full_time_str(ei.occurTime);
    protocol::postEventWithdraw(ei.id, ei.type, szOccur.substr(11,szOccur.length() ) );
}
