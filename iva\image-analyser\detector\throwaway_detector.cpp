
/**
 * Project IVA (image analyser)
 */
#include <algorithm>
#include <chrono>
#include "opencv2/core/core.hpp"
#include <opencv2/opencv.hpp>
#include "module_option.h"
#include "log.h"
#include "throwaway_detector.h"
#include "config/throwaway_config.hpp"
#include "detector/roi_object_detect/roi_object_parms.h"

namespace ia
{
    using namespace std;

    /**
     * @brief 在一帧图像中找到符合阈值要求的所有目标框
     * @param[in] boxes 输入的所有目标框
     * @param[in] roi ROI框
     * @param[in] threshold IOU阈值
     * @return 匹配的目标框列表
     */
    ImageObjectList matchBoxes(const ImageObjectList& boxes, ImageObject roi, float threshold)
    {
        ImageObjectList matchedBoxes;
        for (ImageObject box : boxes)
        {
            float iou = calIOU(box, roi);
            if (iou >= threshold)
            {
                matchedBoxes.emplace_back(box);
            }
        }
        return matchedBoxes;
    }


    ThrowawayDetector::ThrowawayDetector()
    {
        setParams();
    }

    void ThrowawayDetector::reset()
    {
        BaseDetector::reset();
        historyObjects.clear();
        polyMaskInited = false;
    }

    /**
     * @brief  设置路障跟踪模块、路障事件参数
     */
    void ThrowawayDetector::setParams()
    {
        framesNum = THROWAYAY_CFG->framesNum();
        matchedCountAtLeast = THROWAYAY_CFG->matchedCount();
        if (matchedCountAtLeast > framesNum)
            IVA_LOG_ERROR("matchedCountAtLeast {} should not be greater than framesNum {}. Please configure the parameters again. {}", matchedCountAtLeast, framesNum);
        iouThreshold = THROWAYAY_CFG->iouThres();
        isFilterObject = THROWAYAY_CFG->isFilterObject();
        ratioThreshold = THROWAYAY_CFG->ratioThres();
        enablePostprocess = THROWAYAY_CFG->enablePostprocess();
    }

    /**
     * @brief  生成感兴趣掩码
     * @param[in] width,height 帧图像宽高
     */
    void ThrowawayDetector::setInterestedMask(int width, int height)
    {
        auto& regionData = this->regionMask.get();
        if (!polyMaskInited && !regionData.empty())
        {
            polyMaskInited = true;

            interestedMasks.clear();
            for (auto& data : regionData)
            {
                //! cv::Point2d 转换成成 cv::Point
                std::vector<cv::Point> points;
                for (auto& p : data)
                    points.emplace_back(p.x * (float)width, p.y * (float)height);

                //! 生成掩码 检测区为黑色、非检测区为白色
                cv::Mat scaledMask(height, width, CV_8UC1);
                scaledMask *= 0;
                cv::fillConvexPoly(scaledMask, points, 255);
                interestedMasks.emplace_back(std::move(scaledMask));
            }
        }
    }

    /**
     * @brief 过滤掉roi之外的目标框
     * @param[in] objects 推理检测出的目标
     */
    void ThrowawayDetector::filterRects(ImageObjectList& objects)
    {
        for (const auto& interestedMask : interestedMasks)
        {
            for (auto objIt = objects.begin(); objIt != objects.end(); )
            {
                auto& object = *objIt;

                /// 将 object 转换为矩形
                cv::Rect2d rect(object.x, object.y, object.width, object.height);

                // 掩码图区域
                cv::Rect2d maskRect(0,0, interestedMask.size().width, interestedMask.size().height);

                // 求交集 得到有效区域 避免越界
                cv::Rect2d validRect = rect & maskRect;

                int noZeroCount = cv::countNonZero(interestedMask(validRect));
                auto ratio = noZeroCount * 1.0 / (rect.width * rect.height);

                //! 不符合阈值范围的框删除
                if (ratio < ratioThreshold)
                {
                    objIt = objects.erase(objIt);
                    continue;
                }
                objIt++;
            }
        }
    }

    /**
     * @brief  当前目标框和历史目标框IOU匹配，如果多次匹配上则认为是抛洒物
     * @param[in] objects 推理检测出的目标
     * @param[in] frameIndex 当前帧索引
     */
    ImageObjectList ThrowawayDetector::trackerFilter(ImageObjectList &objects, int frameIndex)
    {
        ImageObjectList results{};
        if (objects.empty())
            return results;

        /// 历史框没有缓存，则先缓存
        if ((int)historyObjects.size() < framesNum)
        {
            historyObjects.emplace_back(frameIndex, std::move(objects));
            return results;
        }

        /// 将当前帧的目标依次和缓存的N帧图像中的目标进行匹配，统计匹配的个数(即在N帧中有多少帧出现了该目标)
        for (const auto& currObj : objects)
        {
            int matchedCount = 0;
            for (auto& [frameId, frameObjs] : historyObjects)
            {
                auto matchedBoxes = matchBoxes(frameObjs, currObj, iouThreshold);
                if (!matchedBoxes.empty())
                {
                   if (++matchedCount > matchedCountAtLeast)
                       results.emplace_back(currObj);
                }
            }
        }

        historyObjects.emplace_back(frameIndex, std::move(objects));
        historyObjects.erase(historyObjects.begin());
        return results;
    }

    /**
     * 路障检测函数
     * @param frameData 当前检测帧，包含当前帧的相关信息
     */
    ImageObjectList ThrowawayDetector::detect(const FrameData& frameData)
    {

//        if (frameData.inputObjects.find(DetectorType::Roiobject) == frameData.inputObjects.end())
//            return {};

        /// 清除过期的历史目标框
        if (!historyObjects.empty() && frameData.frameIndex - historyObjects.front().first > (THROWAYAY_CFG->cacheSize() + framesNum) * ROI_OBJECT_DETECT_INTERVAL)
            historyObjects.pop_front();

        auto objectsItr = frameData.inputObjects.find(DetectorType::Roiobject);
        if (objectsItr == frameData.inputObjects.end())
            return {};

        auto objects = objectsItr->second;

        //! 如果不使用后处理，则直接返回推理检测框
        if (!enablePostprocess)
            return objects;

        //! 1、过滤跳帧或非法置信度的box
        objects.erase(std::remove_if(objects.begin(), objects.end(),[&](ImageObject& obj){
            return obj.confidence < 0;}), objects.end());

        if (objects.empty())
            return {};

        //! 2、产生感兴趣区域的mask
        setInterestedMask(frameData.width, frameData.height);

        //! 3、过滤掩码之外的目标
        filterRects(objects);

        //! 4、使用iou过滤
        return isFilterObject ? trackerFilter(objects, frameData.frameIndex) : objects;
    }

}
