cmake_minimum_required (VERSION 3.5.2)
## 目标生成
set(TARGET_LIBRARY "gstivaplugin1")
# 输出目录
set(LIBRARY_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/out/plugins)
set(LIB_IVA ${PROJECT_SOURCE_DIR}/out/lib)

add_compile_options(-std=c++17 -fPIC -Wno-unknown-pragmas -Wno-unused-variable -Werror=return-type -Wall)

# opencv
if(${PLATFORM} MATCHES "HUAWEI")
set(OPENCV_HOME "/home/<USER>/opencv")
else()
set(OPENCV_HOME "/usr/local")
endif()
set(OPENCV_LIB
    opencv_core
    opencv_imgproc
)

find_package(PkgConfig)
pkg_check_modules(GST REQUIRED gstreamer-1.0>=1.4
                               gstreamer-base-1.0>=1.4
                               gstreamer-video-1.0>=1.4)

# 头文件
include_directories(
    ${OPENCV_HOME}/include/
	${PLATFORM_INCLUDES}
    ${GST_INCLUDE_DIRS}
    ${PROJECT_SOURCE_DIR}/iva-log
    ${PROJECT_SOURCE_DIR}/common
    ${PROJECT_SOURCE_DIR}/event-analyser/include
	${PROJECT_SOURCE_DIR}/image-analyser/model_warehouse/
	${PROJECT_SOURCE_DIR}/image-analyser/
    ${PROJECT_SOURCE_DIR}/iva-app/protocol
)

if(${PLATFORM} MATCHES "NVIDIA")
	include_directories(${PROJECT_SOURCE_DIR}/image-analyser/model_warehouse/nvidia)
elseif (${PLATFORM} MATCHES "CAMBRICON")
	include_directories(${PROJECT_SOURCE_DIR}/image-analyser/model_warehouse/cambricon)
endif()

# 库路径
link_directories(
	${PLATFORM_LIBRARIES_DIR}
    ${OPENCV_HOME}/lib/
    ${LIB_IVA}
)

# cpp
FILE(GLOB frame "frame/*.cpp")
if(${PLATFORM} MATCHES "NVIDIA")
	FILE(GLOB record "record/*.cpp")
	FILE(GLOB event "event/*.cpp")
else()
	FILE(GLOB event "event/event_proc.cpp")
endif()

FILE(GLOB src "*.cpp")
SET(ALL_SRC ${include} ${frame} ${event} ${record} ${src})


set(RELATED_LIBS
    pthread stdc++fs ivalog event_analyser iva_protocol image_analyser
)
if(NOT ${PLATFORM} MATCHES "NVIDIA")
list(APPEND RELATED_LIBS aibufsurftransform)
endif()

# 生成动态库
ADD_LIBRARY(${TARGET_LIBRARY} SHARED ${ALL_SRC})
target_link_libraries(${TARGET_LIBRARY} ${OPENCV_LIB} ${PLATFORM_LIBRARIES} ${GST_LIBRARIES} ${RELATED_LIBS})
