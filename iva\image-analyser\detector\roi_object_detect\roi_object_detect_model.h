#pragma once

#include "model_warehouse/nvidia/detect_model.h"
#include "roi_object_parms.h"
#include "util/algorithm_util.h"
#include "config/roiobject_config.hpp"
#include "opencv2/core/core.hpp"
#include <opencv2/opencv.hpp>
#include <string>
#include <vector>


using namespace model_warehouse;
using namespace algorithm_util;

namespace roi_object_detect{

	class RoiObjectDetectModelPrivate
	{
	public:
		static RoiObjectDetectModelPrivate* getInstance()
		{
			static RoiObjectDetectModelPrivate instance;
			return &instance;
		}

        DetectModel* getModel(std::string modelPath,int deviceID, int& ret)
		{
			if (!m_detectModel)
			{
                m_detectModel = new DetectModel(std::move(modelPath),deviceID, ROIOBJECT_CFG->detectScoresThres()
														   ,ROIOBJECT_CFG->detectNmsIouThres()
														   ,ROIOBJECT_CFG->detectPaddingValue()); //isInitDetectModel : initResource of detect model. now is using yolov5s
                m_detectModel->initResource();
			}
			return m_detectModel;
		}

	private:
        DetectModel* m_detectModel;
	};

    class  RoiObjectDetectModel{
    public:
        RoiObjectDetectModel(DetectModelParam& detectModelParam);
        ~RoiObjectDetectModel();

 		//detect the throwing object target from the input picture 
        int detect(ROIObjectParam* roiObjectParam, int deviceID, int& ret);
        
    private:
	    //parameters for detect model
        DetectModelParam m_detectModelParam;
    };
}
