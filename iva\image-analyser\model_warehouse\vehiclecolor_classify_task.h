#pragma once
#include "class_task_base.h"

namespace ia
{
    /**
     * 车辆颜色分类器行为类
     */
    class VehicleColorClassifyAction : public ClassifierActionBase {
    public:
        explicit VehicleColorClassifyAction(int deviceID = 0) 
            : ClassifierActionBase(deviceID, 4, "vehiclecolor") {}
    };

    /**
     * 车辆颜色分类器管理类
     */
    using VehicleColorClassifyMgr = ClassifierManagerBase<VehicleColorClassifyAction>;
}